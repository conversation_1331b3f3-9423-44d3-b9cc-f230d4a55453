import 'package:equatable/equatable.dart';

/// Modèle pour les départements
class DepartmentModel extends Equatable {
  final int id;
  final String name;
  final String? description;
  final String? code;
  final int? managerId;
  final String? managerName;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DepartmentModel({
    required this.id,
    required this.name,
    this.description,
    this.code,
    this.managerId,
    this.managerName,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DepartmentModel.fromJson(Map<String, dynamic> json) {
    return DepartmentModel(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      code: json['code'] as String?,
      managerId: json['manager_id'] as int?,
      managerName: json['manager_name'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'code': code,
      'manager_id': managerId,
      'manager_name': managerName,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  DepartmentModel copyWith({
    int? id,
    String? name,
    String? description,
    String? code,
    int? managerId,
    String? managerName,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DepartmentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      code: code ?? this.code,
      managerId: managerId ?? this.managerId,
      managerName: managerName ?? this.managerName,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        code,
        managerId,
        managerName,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'DepartmentModel(id: $id, name: $name, code: $code, managerId: $managerId)';
  }
}
