import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/core/config/app_config.dart';

// Import all test files
import 'unit/providers/auth_provider_test.dart' as auth_provider_tests;
import 'integration/backend_integration_test.dart' as integration_tests;

/// Script principal pour exécuter tous les tests
void main() {
  group('ClockIn Mobile - All Tests', () {
    setUpAll(() {
      // Configuration globale pour les tests
      AppConfig.setEnvironment(Environment.development);
      print('🚀 Starting ClockIn Mobile Test Suite');
      print('📱 Environment: ${AppConfig.environment}');
      print('🌐 Base URL: ${AppConfig.baseUrl}');
    });

    tearDownAll(() {
      print('✅ ClockIn Mobile Test Suite Completed');
    });

    group('📋 Unit Tests', () {
      print('Running Unit Tests...');
      auth_provider_tests.main();
    });

    group('🔗 Integration Tests', () {
      print('Running Integration Tests...');
      integration_tests.main();
    });
  });
}

/// Utilitaires pour les tests
class TestUtils {
  /// Crée un utilisateur de test
  static Map<String, dynamic> createTestUser({
    int id = 1,
    String name = 'Test User',
    String email = '<EMAIL>',
    String role = 'employee',
    bool isActive = true,
  }) {
    final now = DateTime.now();
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'is_active': isActive,
      'created_at': now.toIso8601String(),
      'updated_at': now.toIso8601String(),
    };
  }

  /// Crée une présence de test
  static Map<String, dynamic> createTestAttendance({
    int id = 1,
    int userId = 1,
    DateTime? date,
    DateTime? checkinTime,
    DateTime? checkoutTime,
    String status = 'present',
  }) {
    final testDate = date ?? DateTime.now();
    return {
      'id': id,
      'user_id': userId,
      'date': testDate.toIso8601String().split('T')[0],
      'checkin_time': checkinTime?.toIso8601String(),
      'checkout_time': checkoutTime?.toIso8601String(),
      'status': status,
      'created_at': testDate.toIso8601String(),
      'updated_at': testDate.toIso8601String(),
    };
  }

  /// Simule une réponse API réussie
  static Map<String, dynamic> createSuccessResponse({
    dynamic data,
    String message = 'Success',
  }) {
    return {
      'success': true,
      'data': data,
      'message': message,
    };
  }

  /// Simule une réponse API d'erreur
  static Map<String, dynamic> createErrorResponse({
    String message = 'Error',
    Map<String, dynamic>? errors,
  }) {
    return {
      'success': false,
      'message': message,
      'errors': errors,
    };
  }

  /// Vérifie la structure d'une réponse API
  static void validateApiResponse(Map<String, dynamic> response) {
    expect(response, contains('success'));
    expect(response['success'], isA<bool>());
    
    if (response['success'] == true) {
      expect(response, anyOf([
        contains('data'),
        contains('message'),
      ]));
    } else {
      expect(response, contains('message'));
      expect(response['message'], isA<String>());
    }
  }

  /// Vérifie la structure d'un utilisateur
  static void validateUserStructure(Map<String, dynamic> user) {
    expect(user, contains('id'));
    expect(user, contains('name'));
    expect(user, contains('email'));
    expect(user, contains('role'));
    expect(user, contains('is_active'));
    expect(user, contains('created_at'));
    expect(user, contains('updated_at'));
    
    expect(user['id'], isA<int>());
    expect(user['name'], isA<String>());
    expect(user['email'], isA<String>());
    expect(user['role'], isA<String>());
    expect(user['is_active'], isA<bool>());
  }

  /// Vérifie la structure d'une présence
  static void validateAttendanceStructure(Map<String, dynamic> attendance) {
    expect(attendance, contains('id'));
    expect(attendance, contains('user_id'));
    expect(attendance, contains('date'));
    expect(attendance, contains('status'));
    expect(attendance, contains('created_at'));
    expect(attendance, contains('updated_at'));
    
    expect(attendance['id'], isA<int>());
    expect(attendance['user_id'], isA<int>());
    expect(attendance['date'], isA<String>());
    expect(attendance['status'], isA<String>());
  }

  /// Attend un délai spécifique (utile pour les tests d'animation)
  static Future<void> wait(Duration duration) async {
    await Future.delayed(duration);
  }

  /// Simule un délai réseau
  static Future<void> simulateNetworkDelay() async {
    await wait(const Duration(milliseconds: 100));
  }

  /// Génère des données de test aléatoires
  static String generateRandomEmail() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'test_$<EMAIL>';
  }

  /// Génère un nom d'utilisateur aléatoire
  static String generateRandomName() {
    final names = ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve', 'Frank'];
    final surnames = ['Smith', 'Johnson', 'Brown', 'Davis', 'Wilson', 'Moore'];
    final timestamp = DateTime.now().millisecondsSinceEpoch % 1000;
    
    return '${names[timestamp % names.length]} ${surnames[timestamp % surnames.length]}';
  }

  /// Vérifie si une chaîne est un email valide
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Vérifie si une chaîne est un token JWT valide (format basique)
  static bool isValidJwtToken(String token) {
    final parts = token.split('.');
    return parts.length == 3;
  }

  /// Crée des coordonnées GPS de test
  static Map<String, double> createTestCoordinates() {
    return {
      'latitude': 48.8566, // Paris
      'longitude': 2.3522,
    };
  }

  /// Crée une adresse de test
  static String createTestAddress() {
    return 'Paris, France';
  }
}

/// Configuration des tests
class TestConfig {
  static const Duration defaultTimeout = Duration(seconds: 30);
  static const Duration networkTimeout = Duration(seconds: 10);
  static const Duration animationTimeout = Duration(milliseconds: 500);
  
  static const String testEmail = '<EMAIL>';
  static const String testPassword = 'password123';
  static const String testName = 'Test User';
  
  static const double testLatitude = 48.8566;
  static const double testLongitude = 2.3522;
  static const String testLocation = 'Paris, France';
}

/// Matchers personnalisés pour les tests
class CustomMatchers {
  /// Vérifie qu'une réponse API est valide
  static Matcher isValidApiResponse() {
    return predicate<Map<String, dynamic>>((response) {
      try {
        TestUtils.validateApiResponse(response);
        return true;
      } catch (e) {
        return false;
      }
    }, 'is a valid API response');
  }

  /// Vérifie qu'un utilisateur est valide
  static Matcher isValidUser() {
    return predicate<Map<String, dynamic>>((user) {
      try {
        TestUtils.validateUserStructure(user);
        return true;
      } catch (e) {
        return false;
      }
    }, 'is a valid user');
  }

  /// Vérifie qu'une présence est valide
  static Matcher isValidAttendance() {
    return predicate<Map<String, dynamic>>((attendance) {
      try {
        TestUtils.validateAttendanceStructure(attendance);
        return true;
      } catch (e) {
        return false;
      }
    }, 'is a valid attendance');
  }
}
