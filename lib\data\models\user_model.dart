import 'package:equatable/equatable.dart';

class UserModel extends Equatable {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? avatar;
  final String role; // 'employee', 'admin', 'manager'
  final bool isActive;
  final String? employeeId;
  final String? department;
  final String? position;
  final DateTime? hireDate;
  final DateTime? emailVerifiedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.avatar,
    required this.role,
    required this.isActive,
    this.employeeId,
    this.department,
    this.position,
    this.hireDate,
    this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as int,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      avatar: json['avatar'] as String?,
      role: json['role'] as String,
      isActive: json['is_active'] as bool? ?? true,
      employeeId: json['employee_id'] as String?,
      department: json['department'] as String?,
      position: json['position'] as String?,
      hireDate: json['hire_date'] != null
          ? DateTime.parse(json['hire_date'] as String)
          : null,
      emailVerifiedAt: json['email_verified_at'] != null
          ? DateTime.parse(json['email_verified_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'avatar': avatar,
      'role': role,
      'is_active': isActive,
      'employee_id': employeeId,
      'department': department,
      'position': position,
      'hire_date': hireDate?.toIso8601String().split('T')[0], // Format YYYY-MM-DD
      'email_verified_at': emailVerifiedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  UserModel copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? avatar,
    String? role,
    bool? isActive,
    String? employeeId,
    String? department,
    String? position,
    DateTime? hireDate,
    DateTime? emailVerifiedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      employeeId: employeeId ?? this.employeeId,
      department: department ?? this.department,
      position: position ?? this.position,
      hireDate: hireDate ?? this.hireDate,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        avatar,
        role,
        isActive,
        employeeId,
        department,
        position,
        hireDate,
        emailVerifiedAt,
        createdAt,
        updatedAt,
      ];

  /// Vérifie si l'utilisateur est un administrateur
  bool get isAdmin => role.toLowerCase() == 'admin';

  /// Vérifie si l'utilisateur est un employé
  bool get isEmployee => role.toLowerCase() == 'employee';

  /// Vérifie si l'utilisateur est un manager
  bool get isManager => role.toLowerCase() == 'manager';

  /// Vérifie si l'utilisateur a des privilèges administratifs
  bool get hasAdminPrivileges => isAdmin || isManager;

  /// Retourne le nom complet avec le rôle
  String get displayName => '$name ($role)';

  /// Retourne l'initiales de l'utilisateur
  String get initials {
    final names = name.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : '?';
  }

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, role: $role, employeeId: $employeeId)';
  }
}

/// Énumération des rôles utilisateur
enum UserRole {
  employee('employee', 'Employé'),
  manager('manager', 'Manager'),
  admin('admin', 'Administrateur');

  const UserRole(this.value, this.displayName);

  final String value;
  final String displayName;

  static UserRole fromString(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return UserRole.admin;
      case 'manager':
        return UserRole.manager;
      case 'employee':
      default:
        return UserRole.employee;
    }
  }
}
