import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/core/utils/date_utils.dart';

void main() {
  group('DateUtils Tests', () {
    final testDate = DateTime(2024, 1, 15, 14, 30, 0);
    final testTime = DateTime(2024, 1, 15, 14, 30, 0);

    group('Date Formatting', () {
      test('should format date correctly', () {
        final formatted = DateUtils.formatDate(testDate);
        expect(formatted, equals('Jan 15, 2024'));
      });

      test('should format date short correctly', () {
        final formatted = DateUtils.formatDateShort(testDate);
        expect(formatted, equals('15/01/2024'));
      });

      test('should format time correctly', () {
        final formatted = DateUtils.formatTime(testTime);
        expect(formatted, equals('2:30 PM'));
      });

      test('should format time 24-hour correctly', () {
        final formatted = DateUtils.formatTime24(testTime);
        expect(formatted, equals('14:30'));
      });

      test('should format date and time correctly', () {
        final formatted = DateUtils.formatDateTime(testDate);
        expect(formatted, equals('Jan 15, 2024 at 2:30 PM'));
      });

      test('should format for API correctly', () {
        final formatted = DateUtils.formatForApi(testDate);
        expect(formatted, isA<String>());
        expect(formatted, contains('2024-01-15'));
      });
    });

    group('Date Parsing', () {
      test('should parse valid date string', () {
        final parsed = DateUtils.parseFromApi('2024-01-15T14:30:00.000Z');
        expect(parsed, isNotNull);
        expect(parsed!.year, equals(2024));
        expect(parsed.month, equals(1));
        expect(parsed.day, equals(15));
      });

      test('should return null for invalid date string', () {
        final parsed = DateUtils.parseFromApi('invalid-date');
        expect(parsed, isNull);
      });

      test('should return null for empty date string', () {
        final parsed = DateUtils.parseFromApi('');
        expect(parsed, isNull);
      });

      test('should return null for null date string', () {
        final parsed = DateUtils.parseFromApi(null);
        expect(parsed, isNull);
      });
    });

    group('Relative Time', () {
      test('should return "Just now" for very recent time', () {
        final now = DateTime.now();
        final recent = now.subtract(const Duration(seconds: 30));
        final relative = DateUtils.getRelativeTime(recent);
        expect(relative, equals('Just now'));
      });

      test('should return minutes ago for recent time', () {
        final now = DateTime.now();
        final recent = now.subtract(const Duration(minutes: 5));
        final relative = DateUtils.getRelativeTime(recent);
        expect(relative, equals('5 minutes ago'));
      });

      test('should return hours ago for older time', () {
        final now = DateTime.now();
        final recent = now.subtract(const Duration(hours: 2));
        final relative = DateUtils.getRelativeTime(recent);
        expect(relative, equals('2 hours ago'));
      });

      test('should return "Yesterday" for yesterday', () {
        final now = DateTime.now();
        final yesterday = now.subtract(const Duration(days: 1));
        final relative = DateUtils.getRelativeTime(yesterday);
        expect(relative, equals('Yesterday'));
      });
    });

    group('Date Checks', () {
      test('should correctly identify today', () {
        final today = DateTime.now();
        expect(DateUtils.isToday(today), isTrue);
        
        final yesterday = today.subtract(const Duration(days: 1));
        expect(DateUtils.isToday(yesterday), isFalse);
      });

      test('should correctly identify yesterday', () {
        final today = DateTime.now();
        final yesterday = today.subtract(const Duration(days: 1));
        expect(DateUtils.isYesterday(yesterday), isTrue);
        
        expect(DateUtils.isYesterday(today), isFalse);
      });
    });

    group('Date Boundaries', () {
      test('should get start of day correctly', () {
        final start = DateUtils.startOfDay(testDate);
        expect(start.hour, equals(0));
        expect(start.minute, equals(0));
        expect(start.second, equals(0));
        expect(start.day, equals(testDate.day));
      });

      test('should get end of day correctly', () {
        final end = DateUtils.endOfDay(testDate);
        expect(end.hour, equals(23));
        expect(end.minute, equals(59));
        expect(end.second, equals(59));
        expect(end.day, equals(testDate.day));
      });

      test('should get start of week correctly', () {
        final start = DateUtils.startOfWeek(testDate);
        expect(start.weekday, equals(1)); // Monday
      });

      test('should get end of week correctly', () {
        final end = DateUtils.endOfWeek(testDate);
        expect(end.weekday, equals(7)); // Sunday
      });

      test('should get start of month correctly', () {
        final start = DateUtils.startOfMonth(testDate);
        expect(start.day, equals(1));
        expect(start.month, equals(testDate.month));
        expect(start.year, equals(testDate.year));
      });

      test('should get end of month correctly', () {
        final end = DateUtils.endOfMonth(testDate);
        expect(end.month, equals(testDate.month));
        expect(end.year, equals(testDate.year));
        expect(end.day, greaterThan(28)); // At least 28 days in a month
      });
    });

    group('Duration Calculations', () {
      test('should calculate working hours correctly', () {
        final start = DateTime(2024, 1, 15, 9, 0);
        final end = DateTime(2024, 1, 15, 17, 30);
        final duration = DateUtils.calculateWorkingHours(start, end);
        expect(duration.inHours, equals(8));
        expect(duration.inMinutes, equals(510)); // 8.5 hours
      });

      test('should format duration correctly', () {
        final duration = const Duration(hours: 8, minutes: 30);
        final formatted = DateUtils.formatDuration(duration);
        expect(formatted, equals('8h 30m'));
      });

      test('should format duration without hours correctly', () {
        final duration = const Duration(minutes: 45);
        final formatted = DateUtils.formatDuration(duration);
        expect(formatted, equals('45m'));
      });
    });

    group('Age Calculation', () {
      test('should calculate age correctly', () {
        final birthDate = DateTime(1990, 6, 15);
        final age = DateUtils.calculateAge(birthDate);
        expect(age, greaterThanOrEqualTo(33)); // As of 2024
      });

      test('should handle birthday not yet reached this year', () {
        final now = DateTime.now();
        final birthDate = DateTime(now.year - 25, now.month + 1, now.day);
        final age = DateUtils.calculateAge(birthDate);
        expect(age, equals(24)); // Birthday hasn't happened yet this year
      });
    });

    group('Calendar Utilities', () {
      test('should get correct days in month', () {
        expect(DateUtils.getDaysInMonth(2024, 1), equals(31)); // January
        expect(DateUtils.getDaysInMonth(2024, 2), equals(29)); // February (leap year)
        expect(DateUtils.getDaysInMonth(2023, 2), equals(28)); // February (non-leap year)
        expect(DateUtils.getDaysInMonth(2024, 4), equals(30)); // April
      });

      test('should correctly identify leap years', () {
        expect(DateUtils.isLeapYear(2024), isTrue);
        expect(DateUtils.isLeapYear(2023), isFalse);
        expect(DateUtils.isLeapYear(2000), isTrue);
        expect(DateUtils.isLeapYear(1900), isFalse);
      });
    });
  });
}
