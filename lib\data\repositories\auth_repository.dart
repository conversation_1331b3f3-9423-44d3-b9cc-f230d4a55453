import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';

class AuthRepository extends ChangeNotifier {
  final AuthService _authService = AuthService();

  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _token;
  UserModel? _user;
  String? _errorMessage;

  // Getters
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get token => _token;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;

  /// Check authentication status on app start
  Future<void> checkAuthStatus() async {
    _setLoading(true);
    _clearError();

    try {
      final isAuth = await _authService.isAuthenticated();
      if (isAuth) {
        _token = await _authService.getToken();
        _user = await _authService.getCurrentUser();
        _isAuthenticated = true;
      } else {
        _isAuthenticated = false;
        _token = null;
        _user = null;
      }
    } catch (e) {
      _setError('Failed to check authentication status: ${e.toString()}');
      _isAuthenticated = false;
    } finally {
      _setLoading(false);
    }
  }

  /// Login user with email and password
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.login(email, password);

      if (result['success']) {
        _token = result['token'];
        _user = result['user'];
        _isAuthenticated = true;

        // Log du rôle utilisateur pour debug
        if (_user != null) {
          final userRole = UserRole.fromString(_user!.role);
          print('Utilisateur connecté: ${_user!.name} (${userRole.displayName})');
        }

        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? 'Login failed');
        return false;
      }
    } catch (e) {
      _setError('Login failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }



  /// Logout user
  Future<void> logout() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.logout();
      _token = null;
      _user = null;
      _isAuthenticated = false;
    } catch (e) {
      _setError('Logout failed: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }



  /// Update user profile
  Future<bool> updateProfile(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.updateProfile(data);

      if (result['success']) {
        _user = result['user'];
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? 'Profile update failed');
        return false;
      }
    } catch (e) {
      _setError('Profile update failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
      );

      if (result['success']) {
        return true;
      } else {
        _setError(result['message'] ?? 'Password change failed');
        return false;
      }
    } catch (e) {
      _setError('Password change failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  // === MÉTHODES POUR LA GESTION DES RÔLES ===

  /// Vérifie si l'utilisateur actuel est un administrateur
  bool get isAdmin => _user?.isAdmin ?? false;

  /// Vérifie si l'utilisateur actuel est un employé
  bool get isEmployee => _user?.isEmployee ?? false;

  /// Vérifie si l'utilisateur actuel est un manager
  bool get isManager => _user?.isManager ?? false;

  /// Vérifie si l'utilisateur a des privilèges administratifs
  bool get hasAdminPrivileges => _user?.hasAdminPrivileges ?? false;

  /// Retourne le rôle de l'utilisateur actuel
  UserRole? get currentUserRole {
    if (_user == null) return null;
    return UserRole.fromString(_user!.role);
  }

  /// Vérifie si l'utilisateur peut accéder aux fonctionnalités admin
  bool canAccessAdminFeatures() {
    return isAuthenticated && hasAdminPrivileges;
  }

  /// Vérifie si l'utilisateur peut gérer les employés
  bool canManageEmployees() {
    return isAuthenticated && (isAdmin || isManager);
  }

  /// Vérifie si l'utilisateur peut voir les rapports
  bool canViewReports() {
    return isAuthenticated && hasAdminPrivileges;
  }

  /// Retourne la route de redirection selon le rôle
  String getHomeRouteForUser() {
    if (!isAuthenticated || _user == null) {
      return '/login';
    }

    if (isAdmin) {
      return '/admin/dashboard';
    } else if (isManager) {
      return '/manager/dashboard';
    } else {
      return '/dashboard';
    }
  }

  void _clearError() {
    _errorMessage = null;
  }
}


