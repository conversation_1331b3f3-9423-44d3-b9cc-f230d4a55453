import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../../core/constants/app_constants.dart';
import '../../core/config/api_endpoints.dart';
import '../../core/utils/exceptions.dart';
import 'http_service.dart';

class AuthService {
  final HttpService _httpService = HttpService();
  
  /// Login user with enhanced error handling
  Future<Map<String, dynamic>> login(String email, String password) async {
    try {
      // Validate input parameters
      if (email.isEmpty || password.isEmpty) {
        return {
          'success': false,
          'message': 'Email et mot de passe requis',
          'error_type': 'validation',
        };
      }

      // Validate email format
      if (!_isValidEmail(email)) {
        return {
          'success': false,
          'message': 'Format d\'email invalide',
          'error_type': 'validation',
        };
      }

      final response = await _httpService.post(
        ApiEndpoints.login,
        data: {
          'email': email.trim().toLowerCase(),
          'password': password,
        },
      );

      // Validate response structure
      if (response['token'] == null || response['user'] == null) {
        return {
          'success': false,
          'message': 'Réponse serveur invalide',
          'error_type': 'server',
        };
      }

      // Save token and user data
      await _saveAuthData(response['token'], response['user']);

      // Set token in HTTP service for future requests
      _httpService.setAuthToken(response['token']);

      return {
        'success': true,
        'token': response['token'],
        'user': UserModel.fromJson(response['user']),
        'message': response['message'] ?? 'Connexion réussie',
      };
    } on NetworkException catch (e) {
      return _handleNetworkError(e);
    } on FormatException catch (e) {
      return {
        'success': false,
        'message': 'Erreur de format de données',
        'error_type': 'format',
        'details': e.toString(),
      };
    } catch (e) {
      return _handleGenericError(e);
    }
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Handle network errors with specific messages
  Map<String, dynamic> _handleNetworkError(NetworkException e) {
    String userMessage;
    String errorType;

    if (e.message.contains('401')) {
      userMessage = 'Email ou mot de passe incorrect';
      errorType = 'authentication';
    } else if (e.message.contains('422')) {
      userMessage = 'Données de connexion invalides';
      errorType = 'validation';
    } else if (e.message.contains('429')) {
      userMessage = 'Trop de tentatives. Veuillez réessayer plus tard';
      errorType = 'rate_limit';
    } else if (e.message.contains('500')) {
      userMessage = 'Erreur serveur. Veuillez réessayer';
      errorType = 'server';
    } else if (e.message.contains('timeout') || e.message.contains('connection')) {
      userMessage = 'Problème de connexion. Vérifiez votre réseau';
      errorType = 'network';
    } else {
      userMessage = 'Erreur de connexion';
      errorType = 'network';
    }

    return {
      'success': false,
      'message': userMessage,
      'error_type': errorType,
      'details': e.message,
    };
  }

  /// Handle generic errors
  Map<String, dynamic> _handleGenericError(dynamic error) {
    String userMessage = 'Une erreur inattendue s\'est produite';
    String errorType = 'unknown';

    if (error.toString().contains('SocketException')) {
      userMessage = 'Pas de connexion internet';
      errorType = 'network';
    } else if (error.toString().contains('TimeoutException')) {
      userMessage = 'Délai d\'attente dépassé';
      errorType = 'timeout';
    } else if (error.toString().contains('HandshakeException')) {
      userMessage = 'Erreur de sécurité SSL';
      errorType = 'ssl';
    }

    return {
      'success': false,
      'message': userMessage,
      'error_type': errorType,
      'details': error.toString(),
    };
  }



  /// Logout user
  Future<Map<String, dynamic>> logout() async {
    try {
      final token = await getToken();

      if (token != null) {
        // Set token for the logout request
        _httpService.setAuthToken(token);

        try {
          await _httpService.post(ApiEndpoints.logout);
        } catch (e) {
          // Continue with logout even if API call fails
          print('Logout API call failed: $e');
        }
      }

      // Clear local data and HTTP service token
      await _clearAuthData();
      _httpService.setAuthToken(null);

      return {
        'success': true,
        'message': 'Logout successful',
      };
    } catch (e) {
      // Clear local data even if there's an error
      await _clearAuthData();
      _httpService.setAuthToken(null);

      return {
        'success': true,
        'message': 'Logout successful',
      };
    }
  }

  // Get current user
  Future<UserModel?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.userKey);
      
      if (userJson != null) {
        final userData = jsonDecode(userJson);
        return UserModel.fromJson(userData);
      }
      
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get stored token
  Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(AppConstants.tokenKey);
    } catch (e) {
      return null;
    }
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }



  /// Update user profile
  Future<Map<String, dynamic>> updateProfile(Map<String, dynamic> data) async {
    try {
      final token = await getToken();

      if (token == null) {
        return {
          'success': false,
          'message': 'Not authenticated',
        };
      }

      // Set token for authenticated request
      _httpService.setAuthToken(token);

      final response = await _httpService.put(
        ApiEndpoints.updateProfile,
        data: data,
      );

      // Update stored user data
      await _saveAuthData(token, response['user']);

      return {
        'success': true,
        'user': UserModel.fromJson(response['user']),
        'message': response['message'] ?? 'Profile updated successfully',
      };
    } on NetworkException catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Profile update failed: ${e.toString()}',
      };
    }
  }

  /// Change password
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    try {
      final token = await getToken();

      if (token == null) {
        return {
          'success': false,
          'message': 'Not authenticated',
        };
      }

      // Set token for authenticated request
      _httpService.setAuthToken(token);

      final response = await _httpService.put(
        ApiEndpoints.changePassword,
        data: {
          'current_password': currentPassword,
          'new_password': newPassword,
          'new_password_confirmation': newPasswordConfirmation,
        },
      );

      return {
        'success': true,
        'message': response['message'] ?? 'Password changed successfully',
      };
    } on NetworkException catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Password change failed: ${e.toString()}',
      };
    }
  }

  // Save authentication data
  Future<void> _saveAuthData(String token, Map<String, dynamic> userData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.tokenKey, token);
    await prefs.setString(AppConstants.userKey, jsonEncode(userData));
  }

  // Clear authentication data
  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConstants.tokenKey);
    await prefs.remove(AppConstants.userKey);
  }

  /// Refresh token
  Future<Map<String, dynamic>> refreshToken() async {
    try {
      final token = await getToken();

      if (token == null) {
        return {
          'success': false,
          'message': 'No token found',
        };
      }

      // Set token for authenticated request
      _httpService.setAuthToken(token);

      final response = await _httpService.post(ApiEndpoints.refreshToken);

      // Save new token and user data
      await _saveAuthData(response['token'], response['user']);

      // Update HTTP service with new token
      _httpService.setAuthToken(response['token']);

      return {
        'success': true,
        'token': response['token'],
        'user': UserModel.fromJson(response['user']),
      };
    } on NetworkException catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Token refresh failed: ${e.toString()}',
      };
    }
  }



  /// Initialize auth service with stored token
  Future<void> initialize() async {
    final token = await getToken();
    if (token != null) {
      _httpService.setAuthToken(token);
    }
  }
}
