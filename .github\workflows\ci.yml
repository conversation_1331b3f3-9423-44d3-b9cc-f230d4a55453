name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        flutter-version: ['3.27.x']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: ${{ matrix.flutter-version }}
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Verify formatting
      run: dart format --output=none --set-exit-if-changed .
      
    - name: Analyze project source
      run: flutter analyze --fatal-infos
      
    - name: Run unit tests
      run: flutter test test/unit/ --coverage
      
    - name: Run widget tests
      run: flutter test test/widget/
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        
    - name: Generate coverage report
      run: |
        sudo apt-get update
        sudo apt-get install -y lcov
        genhtml coverage/lcov.info -o coverage/html
        
    - name: Upload coverage report
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage/html/

  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.27.x'
        channel: 'stable'
        cache: true
        
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '17'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build APK
      run: flutter build apk --release
      
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: release-apk
        path: build/app/outputs/flutter-apk/app-release.apk

  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.27.x'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Build iOS (no codesign)
      run: flutter build ios --release --no-codesign
      
    - name: Upload iOS build
      uses: actions/upload-artifact@v3
      with:
        name: ios-build
        path: build/ios/iphoneos/

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.27.x'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run dart analyze
      run: flutter analyze --fatal-infos
      
    - name: Check formatting
      run: dart format --output=none --set-exit-if-changed .
      
    - name: Check for unused dependencies
      run: flutter pub deps

  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.27.x'
        channel: 'stable'
        cache: true
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Run performance tests
      run: |
        # Placeholder pour les tests de performance
        echo "Performance tests would run here"
        # flutter drive --target=test_driver/app.dart

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, build-android, build-ios]
    if: github.ref == 'refs/heads/develop'
    
    environment:
      name: staging
      url: https://staging.clockin-mobile.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Android APK
      uses: actions/download-artifact@v3
      with:
        name: release-apk
        path: ./artifacts/
        
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Ici, vous ajouteriez les commandes pour déployer vers votre environnement de staging
        # Par exemple, upload vers Firebase App Distribution, TestFlight, etc.

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, build-android, build-ios]
    if: github.ref == 'refs/heads/main'
    
    environment:
      name: production
      url: https://clockin-mobile.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Android APK
      uses: actions/download-artifact@v3
      with:
        name: release-apk
        path: ./artifacts/
        
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Ici, vous ajouteriez les commandes pour déployer vers la production
        # Par exemple, upload vers Google Play Store, App Store, etc.

  notify:
    name: Notify Team
    runs-on: ubuntu-latest
    needs: [test, build-android, build-ios]
    if: always()
    
    steps:
    - name: Notify on success
      if: ${{ needs.test.result == 'success' && needs.build-android.result == 'success' && needs.build-ios.result == 'success' }}
      run: |
        echo "✅ Pipeline succeeded! All tests passed and builds completed successfully."
        # Ici, vous pourriez ajouter une notification Slack, email, etc.
        
    - name: Notify on failure
      if: ${{ needs.test.result == 'failure' || needs.build-android.result == 'failure' || needs.build-ios.result == 'failure' }}
      run: |
        echo "❌ Pipeline failed! Check the logs for details."
        # Ici, vous pourriez ajouter une notification d'échec
