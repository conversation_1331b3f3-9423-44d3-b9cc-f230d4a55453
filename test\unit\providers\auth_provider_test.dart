import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/presentation/providers/auth_provider.dart';
import 'package:clockin_mobile/data/models/user_model.dart';

void main() {
  group('AuthProvider Tests', () {
    late AuthProvider authProvider;

    setUp(() {
      authProvider = AuthProvider();
    });

    tearDown(() {
      authProvider.dispose();
    });

    group('Initial State', () {
      test('should have initial state', () {
        expect(authProvider.state, equals(AuthState.initial));
        expect(authProvider.user, isNull);
        expect(authProvider.token, isNull);
        expect(authProvider.errorMessage, isNull);
        expect(authProvider.isAuthenticated, isFalse);
        expect(authProvider.isLoading, isFalse);
        expect(authProvider.hasError, isFalse);
      });
    });

    group('Authentication States', () {
      test('should clear error state', () {
        // Test de la méthode clearError
        authProvider.clearError();
        expect(authProvider.hasError, isFalse);
        expect(authProvider.errorMessage, isNull);
      });

      test('should validate user model structure', () {
        final testUser = UserModel(
          id: 1,
          name: 'Test User',
          email: '<EMAIL>',
          role: 'employee',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(testUser.id, equals(1));
        expect(testUser.name, equals('Test User'));
        expect(testUser.email, equals('<EMAIL>'));
        expect(testUser.role, equals('employee'));
        expect(testUser.isActive, isTrue);
        expect(testUser.isAdmin, isFalse);
        expect(testUser.isEmployee, isTrue);
      });
    });

    group('User Model Validation', () {
      test('should create user with all fields', () {
        final now = DateTime.now();
        final user = UserModel(
          id: 1,
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+**********',
          avatar: 'avatar.jpg',
          role: 'admin',
          isActive: true,
          employeeId: 'EMP001',
          department: 'IT',
          position: 'Developer',
          hireDate: now,
          emailVerifiedAt: now,
          createdAt: now,
          updatedAt: now,
        );

        expect(user.id, equals(1));
        expect(user.name, equals('John Doe'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.phone, equals('+**********'));
        expect(user.avatar, equals('avatar.jpg'));
        expect(user.role, equals('admin'));
        expect(user.isActive, isTrue);
        expect(user.employeeId, equals('EMP001'));
        expect(user.department, equals('IT'));
        expect(user.position, equals('Developer'));
        expect(user.hireDate, equals(now));
        expect(user.emailVerifiedAt, equals(now));
        expect(user.createdAt, equals(now));
        expect(user.updatedAt, equals(now));
      });

      test('should serialize to JSON correctly', () {
        final now = DateTime.now();
        final user = UserModel(
          id: 1,
          name: 'Test User',
          email: '<EMAIL>',
          role: 'employee',
          isActive: true,
          createdAt: now,
          updatedAt: now,
        );

        final json = user.toJson();
        expect(json['id'], equals(1));
        expect(json['name'], equals('Test User'));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['role'], equals('employee'));
        expect(json['is_active'], isTrue);
        expect(json['created_at'], isNotNull);
        expect(json['updated_at'], isNotNull);
      });

      test('should deserialize from JSON correctly', () {
        final now = DateTime.now();
        final json = {
          'id': 1,
          'name': 'Test User',
          'email': '<EMAIL>',
          'role': 'employee',
          'is_active': true,
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
        };

        final user = UserModel.fromJson(json);
        expect(user.id, equals(1));
        expect(user.name, equals('Test User'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.role, equals('employee'));
        expect(user.isActive, isTrue);
      });

      test('should copy with new values', () {
        final original = UserModel(
          id: 1,
          name: 'Original Name',
          email: '<EMAIL>',
          role: 'employee',
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final copied = original.copyWith(
          name: 'New Name',
          email: '<EMAIL>',
        );

        expect(copied.id, equals(original.id));
        expect(copied.name, equals('New Name'));
        expect(copied.email, equals('<EMAIL>'));
        expect(copied.role, equals(original.role));
        expect(copied.isActive, equals(original.isActive));
      });
    });
  });
}
