import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider pour la gestion des paramètres de l'application
class AppProvider extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  static const String _languageKey = 'language_code';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _biometricKey = 'biometric_enabled';
  static const String _autoCheckoutKey = 'auto_checkout_enabled';
  static const String _locationAccuracyKey = 'location_accuracy';

  // État
  ThemeMode _themeMode = ThemeMode.system;
  String _languageCode = 'fr';
  bool _notificationsEnabled = true;
  bool _biometricEnabled = false;
  bool _autoCheckoutEnabled = false;
  double _locationAccuracy = 10.0;
  bool _isInitialized = false;

  // Getters
  ThemeMode get themeMode => _themeMode;
  String get languageCode => _languageCode;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get biometricEnabled => _biometricEnabled;
  bool get autoCheckoutEnabled => _autoCheckoutEnabled;
  double get locationAccuracy => _locationAccuracy;
  bool get isInitialized => _isInitialized;

  /// Initialise les paramètres depuis le stockage local
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Thème
      final themeIndex = prefs.getInt(_themeKey) ?? ThemeMode.system.index;
      _themeMode = ThemeMode.values[themeIndex];
      
      // Langue
      _languageCode = prefs.getString(_languageKey) ?? 'fr';
      
      // Notifications
      _notificationsEnabled = prefs.getBool(_notificationsKey) ?? true;
      
      // Biométrie
      _biometricEnabled = prefs.getBool(_biometricKey) ?? false;
      
      // Auto checkout
      _autoCheckoutEnabled = prefs.getBool(_autoCheckoutKey) ?? false;
      
      // Précision de localisation
      _locationAccuracy = prefs.getDouble(_locationAccuracyKey) ?? 10.0;
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation des paramètres: $e');
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// Change le mode de thème
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_themeKey, mode.index);
      } catch (e) {
        debugPrint('Erreur lors de la sauvegarde du thème: $e');
      }
    }
  }

  /// Change la langue
  Future<void> setLanguage(String languageCode) async {
    if (_languageCode != languageCode) {
      _languageCode = languageCode;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_languageKey, languageCode);
      } catch (e) {
        debugPrint('Erreur lors de la sauvegarde de la langue: $e');
      }
    }
  }

  /// Active/désactive les notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled != enabled) {
      _notificationsEnabled = enabled;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_notificationsKey, enabled);
      } catch (e) {
        debugPrint('Erreur lors de la sauvegarde des notifications: $e');
      }
    }
  }

  /// Active/désactive l'authentification biométrique
  Future<void> setBiometricEnabled(bool enabled) async {
    if (_biometricEnabled != enabled) {
      _biometricEnabled = enabled;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_biometricKey, enabled);
      } catch (e) {
        debugPrint('Erreur lors de la sauvegarde de la biométrie: $e');
      }
    }
  }

  /// Active/désactive le checkout automatique
  Future<void> setAutoCheckoutEnabled(bool enabled) async {
    if (_autoCheckoutEnabled != enabled) {
      _autoCheckoutEnabled = enabled;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_autoCheckoutKey, enabled);
      } catch (e) {
        debugPrint('Erreur lors de la sauvegarde du checkout automatique: $e');
      }
    }
  }

  /// Définit la précision de localisation
  Future<void> setLocationAccuracy(double accuracy) async {
    if (_locationAccuracy != accuracy) {
      _locationAccuracy = accuracy;
      notifyListeners();
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setDouble(_locationAccuracyKey, accuracy);
      } catch (e) {
        debugPrint('Erreur lors de la sauvegarde de la précision: $e');
      }
    }
  }

  /// Réinitialise tous les paramètres
  Future<void> resetSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_themeKey);
      await prefs.remove(_languageKey);
      await prefs.remove(_notificationsKey);
      await prefs.remove(_biometricKey);
      await prefs.remove(_autoCheckoutKey);
      await prefs.remove(_locationAccuracyKey);
      
      // Réinitialiser les valeurs par défaut
      _themeMode = ThemeMode.system;
      _languageCode = 'fr';
      _notificationsEnabled = true;
      _biometricEnabled = false;
      _autoCheckoutEnabled = false;
      _locationAccuracy = 10.0;
      
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de la réinitialisation: $e');
    }
  }

  /// Exporte les paramètres
  Map<String, dynamic> exportSettings() {
    return {
      'theme_mode': _themeMode.index,
      'language_code': _languageCode,
      'notifications_enabled': _notificationsEnabled,
      'biometric_enabled': _biometricEnabled,
      'auto_checkout_enabled': _autoCheckoutEnabled,
      'location_accuracy': _locationAccuracy,
    };
  }

  /// Importe les paramètres
  Future<void> importSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (settings.containsKey('theme_mode')) {
        final themeIndex = settings['theme_mode'] as int;
        if (themeIndex >= 0 && themeIndex < ThemeMode.values.length) {
          _themeMode = ThemeMode.values[themeIndex];
          await prefs.setInt(_themeKey, themeIndex);
        }
      }
      
      if (settings.containsKey('language_code')) {
        _languageCode = settings['language_code'] as String;
        await prefs.setString(_languageKey, _languageCode);
      }
      
      if (settings.containsKey('notifications_enabled')) {
        _notificationsEnabled = settings['notifications_enabled'] as bool;
        await prefs.setBool(_notificationsKey, _notificationsEnabled);
      }
      
      if (settings.containsKey('biometric_enabled')) {
        _biometricEnabled = settings['biometric_enabled'] as bool;
        await prefs.setBool(_biometricKey, _biometricEnabled);
      }
      
      if (settings.containsKey('auto_checkout_enabled')) {
        _autoCheckoutEnabled = settings['auto_checkout_enabled'] as bool;
        await prefs.setBool(_autoCheckoutKey, _autoCheckoutEnabled);
      }
      
      if (settings.containsKey('location_accuracy')) {
        _locationAccuracy = settings['location_accuracy'] as double;
        await prefs.setDouble(_locationAccuracyKey, _locationAccuracy);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'importation: $e');
    }
  }

  /// Vérifie si le mode sombre est actif
  bool isDarkMode(BuildContext context) {
    switch (_themeMode) {
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
      case ThemeMode.system:
        return MediaQuery.of(context).platformBrightness == Brightness.dark;
    }
  }

  /// Bascule entre les modes de thème
  Future<void> toggleTheme() async {
    switch (_themeMode) {
      case ThemeMode.system:
        await setThemeMode(ThemeMode.light);
        break;
      case ThemeMode.light:
        await setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        await setThemeMode(ThemeMode.system);
        break;
    }
  }

  /// Obtient le nom du thème actuel
  String getThemeName() {
    switch (_themeMode) {
      case ThemeMode.system:
        return 'Système';
      case ThemeMode.light:
        return 'Clair';
      case ThemeMode.dark:
        return 'Sombre';
    }
  }
}
