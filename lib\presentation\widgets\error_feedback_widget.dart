import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';

/// Widget pour afficher des messages d'erreur avec feedback visuel
class ErrorFeedbackWidget extends StatefulWidget {
  final String message;
  final String? errorType;
  final VoidCallback? onDismiss;
  final Duration duration;

  const ErrorFeedbackWidget({
    super.key,
    required this.message,
    this.errorType,
    this.onDismiss,
    this.duration = const Duration(seconds: 5),
  });

  @override
  State<ErrorFeedbackWidget> createState() => _ErrorFeedbackWidgetState();
}

class _ErrorFeedbackWidgetState extends State<ErrorFeedbackWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Démarrer l'animation d'entrée
    _animationController.forward();

    // Auto-dismiss après la durée spécifiée
    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _animationController.reverse();
    if (mounted) {
      widget.onDismiss?.call();
    }
  }

  IconData _getErrorIcon() {
    switch (widget.errorType) {
      case 'network':
        return Icons.wifi_off;
      case 'authentication':
        return Icons.lock_outline;
      case 'validation':
        return Icons.warning_outlined;
      case 'rate_limit':
        return Icons.timer_outlined;
      case 'server':
        return Icons.dns_outlined;
      default:
        return Icons.error_outline;
    }
  }

  Color _getErrorColor(ThemeData theme) {
    switch (widget.errorType) {
      case 'network':
        return Colors.orange;
      case 'authentication':
        return theme.colorScheme.error;
      case 'validation':
        return Colors.amber;
      case 'rate_limit':
        return Colors.deepOrange;
      case 'server':
        return Colors.red;
      default:
        return theme.colorScheme.error;
    }
  }

  String _getErrorTitle() {
    switch (widget.errorType) {
      case 'network':
        return 'Problème de connexion';
      case 'authentication':
        return 'Erreur d\'authentification';
      case 'validation':
        return 'Données invalides';
      case 'rate_limit':
        return 'Trop de tentatives';
      case 'server':
        return 'Erreur serveur';
      default:
        return 'Erreur';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorColor = _getErrorColor(theme);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 100),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(AppConstants.spacingM),
              padding: const EdgeInsets.all(AppConstants.spacingM),
              decoration: BoxDecoration(
                color: errorColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(
                  color: errorColor.withValues(alpha: 0.3),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: errorColor.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(AppConstants.spacingS),
                    decoration: BoxDecoration(
                      color: errorColor.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _getErrorIcon(),
                      color: errorColor,
                      size: AppConstants.iconSize,
                    ),
                  ),
                  const SizedBox(width: AppConstants.spacingM),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _getErrorTitle(),
                          style: theme.textTheme.titleSmall?.copyWith(
                            color: errorColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: AppConstants.spacingXS),
                        Text(
                          widget.message,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: _dismiss,
                    icon: Icon(
                      Icons.close,
                      color: errorColor,
                      size: AppConstants.iconSizeS,
                    ),
                    tooltip: 'Fermer',
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Widget pour afficher des messages de succès
class SuccessFeedbackWidget extends StatefulWidget {
  final String message;
  final VoidCallback? onDismiss;
  final Duration duration;

  const SuccessFeedbackWidget({
    super.key,
    required this.message,
    this.onDismiss,
    this.duration = const Duration(seconds: 3),
  });

  @override
  State<SuccessFeedbackWidget> createState() => _SuccessFeedbackWidgetState();
}

class _SuccessFeedbackWidgetState extends State<SuccessFeedbackWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    Future.delayed(widget.duration, () {
      if (mounted) {
        _dismiss();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() async {
    await _animationController.reverse();
    if (mounted) {
      widget.onDismiss?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final successColor = Colors.green;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              margin: const EdgeInsets.all(AppConstants.spacingM),
              padding: const EdgeInsets.all(AppConstants.spacingM),
              decoration: BoxDecoration(
                color: successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(
                  color: successColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    color: successColor,
                    size: AppConstants.iconSize,
                  ),
                  const SizedBox(width: AppConstants.spacingM),
                  Expanded(
                    child: Text(
                      widget.message,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
