import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';

class LocationService {
  // Check if location services are enabled
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  // Check location permission
  Future<LocationPermission> checkPermission() async {
    return await Geolocator.checkPermission();
  }

  // Request location permission
  Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  // Request location permission and return boolean result
  Future<bool> requestLocationPermission() async {
    final permission = await Geolocator.requestPermission();
    return permission == LocationPermission.always ||
           permission == LocationPermission.whileInUse;
  }

  // Get current position
  Future<Map<String, dynamic>> getCurrentPosition() async {
    try {
      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return {
            'success': false,
            'message': 'Location permission denied'
          };
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return {
          'success': false,
          'message': 'Location permission permanently denied'
        };
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Save the position for future use
      await _saveLastLocation(position);

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      String location = 'Unknown location';
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        location = '${place.street ?? ''}, ${place.locality ?? ''}, ${place.country ?? ''}';
      }

      return {
        'success': true,
        'latitude': position.latitude,
        'longitude': position.longitude,
        'location': location,
        'accuracy': position.accuracy,
        'timestamp': position.timestamp,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error getting current position: $e'
      };
    }
  }

  // Get last known position
  Future<Map<String, dynamic>> getLastKnownPosition() async {
    try {
      final position = await Geolocator.getLastKnownPosition();
      
      if (position != null) {
        return {
          'success': true,
          'position': position,
          'latitude': position.latitude,
          'longitude': position.longitude,
          'accuracy': position.accuracy,
          'timestamp': position.timestamp,
        };
      } else {
        // Try to get from shared preferences
        final savedLocation = await _getLastLocation();
        if (savedLocation != null) {
          return {
            'success': true,
            'position': savedLocation,
            'latitude': savedLocation.latitude,
            'longitude': savedLocation.longitude,
            'accuracy': savedLocation.accuracy,
            'timestamp': savedLocation.timestamp,
          };
        }
        
        return {
          'success': false,
          'message': 'No last known position available',
          'error': 'NO_LAST_POSITION',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get last known location: ${e.toString()}',
        'error': 'LOCATION_ERROR',
      };
    }
  }

  // Calculate distance between two points in meters
  double calculateDistance(double startLatitude, double startLongitude,
                          double endLatitude, double endLongitude) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude
    );
  }

  // Check if current location is within specified radius of target location
  Future<Map<String, dynamic>> isWithinRadius({
    required double targetLatitude,
    required double targetLongitude,
    required double radiusInMeters,
  }) async {
    try {
      final currentLocationResult = await getCurrentPosition();

      if (!currentLocationResult['success']) {
        return {
          'success': false,
          'message': 'Failed to get current location',
          'error': currentLocationResult['message'],
        };
      }

      final distance = calculateDistance(
        currentLocationResult['latitude'],
        currentLocationResult['longitude'],
        targetLatitude,
        targetLongitude,
      );

      return {
        'success': true,
        'isWithinRadius': distance <= radiusInMeters,
        'distance': distance,
        'radiusInMeters': radiusInMeters,
        'currentLatitude': currentLocationResult['latitude'],
        'currentLongitude': currentLocationResult['longitude'],
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error checking location radius: $e',
        'error': 'RADIUS_CHECK_ERROR',
      };
    }
  }

  // Get location settings status
  Future<Map<String, dynamic>> getLocationStatus() async {
    try {
      final isServiceEnabled = await Geolocator.isLocationServiceEnabled();
      final permission = await Geolocator.checkPermission();

      return {
        'success': true,
        'isServiceEnabled': isServiceEnabled,
        'permission': permission.toString(),
        'hasPermission': permission == LocationPermission.always ||
                        permission == LocationPermission.whileInUse,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Error getting location status: $e',
        'error': 'STATUS_ERROR',
      };
    }
  }

  // Save last location to shared preferences
  Future<void> _saveLastLocation(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationData = {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'altitude': position.altitude,
        'altitudeAccuracy': position.altitudeAccuracy,
        'speed': position.speed,
        'speedAccuracy': position.speedAccuracy,
        'heading': position.heading,
        'headingAccuracy': position.headingAccuracy,
        'timestamp': position.timestamp.millisecondsSinceEpoch,
      };

      await prefs.setString(AppConstants.locationKey, jsonEncode(locationData));
    } catch (e) {
      // Ignore errors when saving location
    }
  }

  // Get last location from shared preferences
  Future<Position?> _getLastLocation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final locationString = prefs.getString(AppConstants.locationKey);

      if (locationString != null && locationString.isNotEmpty) {
        final locationData = jsonDecode(locationString) as Map<String, dynamic>;

        return Position(
          latitude: locationData['latitude']?.toDouble() ?? 0.0,
          longitude: locationData['longitude']?.toDouble() ?? 0.0,
          timestamp: locationData['timestamp'] != null
              ? DateTime.fromMillisecondsSinceEpoch(locationData['timestamp'])
              : DateTime.now(),
          accuracy: locationData['accuracy']?.toDouble() ?? 0.0,
          altitude: locationData['altitude']?.toDouble() ?? 0.0,
          altitudeAccuracy: locationData['altitudeAccuracy']?.toDouble() ?? 0.0,
          heading: locationData['heading']?.toDouble() ?? 0.0,
          headingAccuracy: locationData['headingAccuracy']?.toDouble() ?? 0.0,
          speed: locationData['speed']?.toDouble() ?? 0.0,
          speedAccuracy: locationData['speedAccuracy']?.toDouble() ?? 0.0,
        );
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}

