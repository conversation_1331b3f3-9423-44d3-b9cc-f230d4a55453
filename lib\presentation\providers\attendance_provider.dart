import 'package:flutter/foundation.dart';
import '../../data/models/attendance_model.dart';
import '../../data/services/attendance_service.dart';
import '../../core/utils/exceptions.dart';

/// États des présences
enum AttendanceState {
  initial,
  loading,
  loaded,
  error,
  checkingIn,
  checkingOut,
}

/// Provider pour la gestion des présences
class AttendanceProvider extends ChangeNotifier {
  final AttendanceService _attendanceService = AttendanceService();

  // État
  AttendanceState _state = AttendanceState.initial;
  AttendanceModel? _todayAttendance;
  List<AttendanceModel> _attendanceHistory = [];
  Map<String, dynamic>? _attendanceStats;
  String? _errorMessage;
  bool _isCheckedIn = false;
  bool _isCheckedOut = false;

  // Pagination pour l'historique
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // Getters
  AttendanceState get state => _state;
  AttendanceModel? get todayAttendance => _todayAttendance;
  List<AttendanceModel> get attendanceHistory => List.unmodifiable(_attendanceHistory);
  Map<String, dynamic>? get attendanceStats => _attendanceStats;
  String? get errorMessage => _errorMessage;
  bool get isCheckedIn => _isCheckedIn;
  bool get isCheckedOut => _isCheckedOut;
  bool get isLoading => _state == AttendanceState.loading;
  bool get hasError => _state == AttendanceState.error;
  bool get hasMoreData => _hasMoreData;
  bool get isLoadingMore => _isLoadingMore;

  /// Initialise le provider
  Future<void> initialize() async {
    await _attendanceService.initialize();
    await loadTodayAttendance();
  }

  /// Charge la présence d'aujourd'hui
  Future<void> loadTodayAttendance() async {
    _setState(AttendanceState.loading);
    _clearError();

    try {
      final result = await _attendanceService.getTodayAttendance();
      
      if (result['success'] == true) {
        _todayAttendance = result['attendance'] as AttendanceModel?;
        _updateCheckStatus();
        _setState(AttendanceState.loaded);
      } else {
        _setError(result['message'] ?? 'Erreur de chargement');
      }
    } on NetworkException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('Erreur de chargement: ${e.toString()}');
    }
  }

  /// Check-in
  Future<bool> checkIn({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    _setState(AttendanceState.checkingIn);
    _clearError();

    try {
      final result = await _attendanceService.checkIn(
        latitude: latitude,
        longitude: longitude,
        location: location,
        notes: notes,
      );
      
      if (result['success'] == true) {
        _todayAttendance = result['attendance'] as AttendanceModel;
        _updateCheckStatus();
        _setState(AttendanceState.loaded);
        return true;
      } else {
        _setError(result['message'] ?? 'Erreur de check-in');
        return false;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erreur de check-in: ${e.toString()}');
      return false;
    }
  }

  /// Check-out
  Future<bool> checkOut({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    _setState(AttendanceState.checkingOut);
    _clearError();

    try {
      final result = await _attendanceService.checkOut(
        latitude: latitude,
        longitude: longitude,
        location: location,
        notes: notes,
      );
      
      if (result['success'] == true) {
        _todayAttendance = result['attendance'] as AttendanceModel;
        _updateCheckStatus();
        _setState(AttendanceState.loaded);
        return true;
      } else {
        _setError(result['message'] ?? 'Erreur de check-out');
        return false;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erreur de check-out: ${e.toString()}');
      return false;
    }
  }

  /// Charge l'historique des présences
  Future<void> loadAttendanceHistory({
    DateTime? startDate,
    DateTime? endDate,
    bool refresh = false,
  }) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _attendanceHistory.clear();
    }

    if (!_hasMoreData || _isLoadingMore) return;

    _isLoadingMore = true;
    if (_attendanceHistory.isEmpty) {
      _setState(AttendanceState.loading);
    }
    _clearError();
    notifyListeners();

    try {
      final result = await _attendanceService.getAttendanceHistory(
        startDate: startDate,
        endDate: endDate,
        page: _currentPage,
        perPage: 20,
      );
      
      if (result['success'] == true) {
        final newAttendances = result['data'] as List<AttendanceModel>;
        
        if (refresh) {
          _attendanceHistory = newAttendances;
        } else {
          _attendanceHistory.addAll(newAttendances);
        }
        
        _currentPage++;
        _hasMoreData = newAttendances.length >= 20;
        _setState(AttendanceState.loaded);
      } else {
        _setError(result['message'] ?? 'Erreur de chargement de l\'historique');
      }
    } on NetworkException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('Erreur de chargement: ${e.toString()}');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// Charge les statistiques de présence
  Future<void> loadAttendanceStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    _setState(AttendanceState.loading);
    _clearError();

    try {
      final result = await _attendanceService.getAttendanceStats(
        startDate: startDate,
        endDate: endDate,
      );
      
      if (result['success'] == true) {
        _attendanceStats = result['stats'] as Map<String, dynamic>;
        _setState(AttendanceState.loaded);
      } else {
        _setError(result['message'] ?? 'Erreur de chargement des statistiques');
      }
    } on NetworkException catch (e) {
      _setError(e.message);
    } catch (e) {
      _setError('Erreur de chargement: ${e.toString()}');
    }
  }

  /// Met à jour les notes d'une présence
  Future<bool> updateAttendanceNotes({
    required int attendanceId,
    required String notes,
  }) async {
    _clearError();

    try {
      final result = await _attendanceService.updateAttendanceNotes(
        attendanceId: attendanceId,
        notes: notes,
      );
      
      if (result['success'] == true) {
        final updatedAttendance = result['attendance'] as AttendanceModel;
        
        // Mettre à jour dans l'historique
        final index = _attendanceHistory.indexWhere((a) => a.id == attendanceId);
        if (index != -1) {
          _attendanceHistory[index] = updatedAttendance;
        }
        
        // Mettre à jour la présence d'aujourd'hui si c'est la même
        if (_todayAttendance?.id == attendanceId) {
          _todayAttendance = updatedAttendance;
        }
        
        notifyListeners();
        return true;
      } else {
        _setError(result['message'] ?? 'Erreur de mise à jour');
        return false;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erreur de mise à jour: ${e.toString()}');
      return false;
    }
  }

  /// Génère un rapport de présence
  Future<String?> generateReport({
    required DateTime startDate,
    required DateTime endDate,
    String format = 'pdf',
  }) async {
    _clearError();

    try {
      final result = await _attendanceService.generateAttendanceReport(
        startDate: startDate,
        endDate: endDate,
        format: format,
      );
      
      if (result['success'] == true) {
        return result['report_url'] as String;
      } else {
        _setError(result['message'] ?? 'Erreur de génération du rapport');
        return null;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return null;
    } catch (e) {
      _setError('Erreur de génération: ${e.toString()}');
      return null;
    }
  }

  /// Met à jour le statut de check-in/out
  void _updateCheckStatus() {
    if (_todayAttendance != null) {
      _isCheckedIn = _todayAttendance!.isCheckedIn;
      _isCheckedOut = _todayAttendance!.isCheckedOut;
    } else {
      _isCheckedIn = false;
      _isCheckedOut = false;
    }
  }

  /// Définit l'état
  void _setState(AttendanceState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// Définit une erreur
  void _setError(String message) {
    _errorMessage = message;
    _setState(AttendanceState.error);
  }

  /// Efface l'erreur
  void _clearError() {
    _errorMessage = null;
  }

  /// Efface l'état d'erreur
  void clearError() {
    if (_state == AttendanceState.error) {
      _setState(AttendanceState.loaded);
    }
    _clearError();
  }

  /// Actualise toutes les données
  Future<void> refresh() async {
    await loadTodayAttendance();
    await loadAttendanceHistory(refresh: true);
  }


}
