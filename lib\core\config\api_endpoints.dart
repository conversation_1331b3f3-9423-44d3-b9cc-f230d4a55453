/// Configuration des endpoints API pour le backend Laravel ClockIn
class ApiEndpoints {
  // Base URL sera fournie par AppConfig
  
  // === AUTHENTIFICATION ===
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String verifyEmail = '/auth/verify-email';
  
  // === PROFIL UTILISATEUR ===
  static const String profile = '/user/profile';
  static const String updateProfile = '/user/profile';
  static const String changePassword = '/user/change-password';
  static const String uploadAvatar = '/user/avatar';
  
  // === PRÉSENCES ===
  static const String checkin = '/attendance/checkin';
  static const String checkout = '/attendance/checkout';
  static const String todayAttendance = '/attendance/today';
  static const String attendanceHistory = '/attendance/history';
  static const String attendanceStats = '/attendance/stats';
  static const String attendanceReport = '/attendance/report';
  
  // === GESTION EMPLOYÉS (Admin) ===
  static const String employees = '/admin/employees';
  static const String employeeDetails = '/admin/employees'; // + /{id}
  static const String createEmployee = '/admin/employees';
  static const String updateEmployee = '/admin/employees'; // + /{id}
  static const String deleteEmployee = '/admin/employees'; // + /{id}
  static const String employeeAttendance = '/admin/employees'; // + /{id}/attendance
  
  // === RAPPORTS (Admin) ===
  static const String dashboardStats = '/admin/dashboard';
  static const String attendanceReports = '/admin/reports/attendance';
  static const String employeeReports = '/admin/reports/employees';
  static const String exportReport = '/admin/reports/export';
  
  // === DÉPARTEMENTS (Admin) ===
  static const String departments = '/admin/departments';
  static const String createDepartment = '/admin/departments';
  static const String updateDepartment = '/admin/departments'; // + /{id}
  static const String deleteDepartment = '/admin/departments'; // + /{id}
  
  // === PARAMÈTRES (Admin) ===
  static const String settings = '/admin/settings';
  static const String updateSettings = '/admin/settings';
  static const String workingHours = '/admin/working-hours';
  static const String holidays = '/admin/holidays';
  
  // === NOTIFICATIONS ===
  static const String notifications = '/notifications';
  static const String markAsRead = '/notifications'; // + /{id}/read
  static const String markAllAsRead = '/notifications/read-all';
  
  // === UTILITAIRES ===
  static const String health = '/health';
  static const String version = '/version';
  
  /// Construit l'URL complète pour un endpoint
  static String buildUrl(String endpoint, {Map<String, dynamic>? params}) {
    String url = endpoint;
    
    if (params != null && params.isNotEmpty) {
      final queryString = params.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');
      url += '?$queryString';
    }
    
    return url;
  }
  
  /// Construit l'URL pour un endpoint avec ID
  static String buildUrlWithId(String endpoint, dynamic id) {
    return '$endpoint/$id';
  }
  
  /// Construit l'URL pour un endpoint avec ID et sous-ressource
  static String buildUrlWithIdAndResource(String endpoint, dynamic id, String resource) {
    return '$endpoint/$id/$resource';
  }
}

/// Paramètres de requête communs
class ApiParams {
  // Pagination
  static const String page = 'page';
  static const String perPage = 'per_page';
  static const String limit = 'limit';
  static const String offset = 'offset';
  
  // Tri
  static const String sortBy = 'sort_by';
  static const String sortOrder = 'sort_order';
  
  // Filtres
  static const String search = 'search';
  static const String filter = 'filter';
  static const String dateFrom = 'date_from';
  static const String dateTo = 'date_to';
  static const String status = 'status';
  static const String department = 'department';
  static const String role = 'role';
  
  // Inclusions
  static const String include = 'include';
  static const String with_ = 'with';
}

/// Codes de statut HTTP personnalisés
class ApiStatusCodes {
  static const int success = 200;
  static const int created = 201;
  static const int noContent = 204;
  static const int badRequest = 400;
  static const int unauthorized = 401;
  static const int forbidden = 403;
  static const int notFound = 404;
  static const int unprocessableEntity = 422;
  static const int internalServerError = 500;
  static const int serviceUnavailable = 503;
}

/// Messages d'erreur API
class ApiErrorMessages {
  static const String networkError = 'Erreur de connexion réseau';
  static const String serverError = 'Erreur serveur';
  static const String unauthorized = 'Non autorisé';
  static const String forbidden = 'Accès interdit';
  static const String notFound = 'Ressource non trouvée';
  static const String validationError = 'Erreur de validation';
  static const String unknownError = 'Erreur inconnue';
  
  static String getErrorMessage(int statusCode) {
    switch (statusCode) {
      case ApiStatusCodes.badRequest:
        return 'Requête invalide';
      case ApiStatusCodes.unauthorized:
        return unauthorized;
      case ApiStatusCodes.forbidden:
        return forbidden;
      case ApiStatusCodes.notFound:
        return notFound;
      case ApiStatusCodes.unprocessableEntity:
        return validationError;
      case ApiStatusCodes.internalServerError:
        return serverError;
      case ApiStatusCodes.serviceUnavailable:
        return 'Service temporairement indisponible';
      default:
        return unknownError;
    }
  }
}

/// Headers HTTP communs
class ApiHeaders {
  static const String contentType = 'Content-Type';
  static const String authorization = 'Authorization';
  static const String accept = 'Accept';
  static const String userAgent = 'User-Agent';
  static const String xRequestedWith = 'X-Requested-With';
  
  static const String applicationJson = 'application/json';
  static const String multipartFormData = 'multipart/form-data';
  static const String xmlHttpRequest = 'XMLHttpRequest';
  
  /// Headers par défaut pour les requêtes API
  static Map<String, String> get defaultHeaders => {
    contentType: applicationJson,
    accept: applicationJson,
    xRequestedWith: xmlHttpRequest,
    userAgent: 'ClockIn-Mobile/1.0.0',
  };
  
  /// Headers avec token d'authentification
  static Map<String, String> withAuth(String token) => {
    ...defaultHeaders,
    authorization: 'Bearer $token',
  };
}
