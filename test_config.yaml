# Configuration pour les tests automatisés
name: ClockIn Mobile Tests
description: Configuration des tests pour l'application ClockIn Mobile

# Paramètres généraux des tests
test_settings:
  timeout: 30000  # 30 secondes
  retry_count: 3
  parallel_execution: true
  coverage_threshold: 80  # Pourcentage minimum de couverture de code

# Tests unitaires
unit_tests:
  enabled: true
  path: "test/unit/"
  patterns:
    - "**/*_test.dart"
  exclude:
    - "test/integration/"
    - "test/widget/"
  
  # Catégories de tests unitaires
  categories:
    models:
      path: "test/unit/models/"
      description: "Tests des modèles de données"
    services:
      path: "test/unit/services/"
      description: "Tests des services API"
    repositories:
      path: "test/unit/repositories/"
      description: "Tests des repositories"
    utils:
      path: "test/unit/utils/"
      description: "Tests des utilitaires"

# Tests de widgets
widget_tests:
  enabled: true
  path: "test/widget/"
  patterns:
    - "**/*_test.dart"
  
  # Catégories de tests de widgets
  categories:
    common:
      path: "test/widget/common/"
      description: "Tests des widgets communs"
    screens:
      path: "test/widget/screens/"
      description: "Tests des écrans"
    forms:
      path: "test/widget/forms/"
      description: "Tests des formulaires"

# Tests d'intégration
integration_tests:
  enabled: true
  path: "test/integration/"
  patterns:
    - "**/*_test.dart"
  
  # Scénarios d'intégration
  scenarios:
    auth_flow:
      description: "Flux d'authentification complet"
      steps:
        - "Login avec credentials valides"
        - "Navigation vers dashboard"
        - "Logout"
    
    attendance_flow:
      description: "Flux de présence complet"
      steps:
        - "Check-in avec géolocalisation"
        - "Vérification du statut"
        - "Check-out"
    
    admin_flow:
      description: "Flux administrateur"
      steps:
        - "Login admin"
        - "Consultation des rapports"
        - "Gestion des employés"

# Configuration de la couverture de code
coverage:
  enabled: true
  output_directory: "coverage/"
  formats:
    - "lcov"
    - "html"
  
  # Exclusions de la couverture
  exclude:
    - "lib/main.dart"
    - "lib/**/*.g.dart"
    - "lib/**/*.freezed.dart"
    - "test/**"
  
  # Seuils de couverture par catégorie
  thresholds:
    global: 80
    models: 90
    services: 85
    repositories: 85
    utils: 90
    widgets: 75

# Configuration des mocks
mocks:
  enabled: true
  auto_generate: true
  output_directory: "test/mocks/"
  
  # Services à mocker
  services:
    - "AuthService"
    - "AttendanceService"
    - "LocationService"
  
  # Repositories à mocker
  repositories:
    - "AuthRepository"
    - "AttendanceRepository"

# Configuration des données de test
test_data:
  fixtures_path: "test/fixtures/"
  
  # Utilisateurs de test
  users:
    employee:
      email: "<EMAIL>"
      password: "password123"
      role: "employee"
    
    admin:
      email: "<EMAIL>"
      password: "admin123"
      role: "admin"
  
  # Données d'attendance de test
  attendance:
    sample_checkin:
      latitude: 40.7128
      longitude: -74.0060
      location: "Test Office"
      timestamp: "2024-01-15T09:00:00.000Z"
    
    sample_checkout:
      latitude: 40.7128
      longitude: -74.0060
      location: "Test Office"
      timestamp: "2024-01-15T17:30:00.000Z"

# Configuration des environnements de test
environments:
  unit:
    api_base_url: "http://localhost:8000/api"
    mock_responses: true
    
  integration:
    api_base_url: "http://test-api.clockin.com/api"
    mock_responses: false
    
  e2e:
    api_base_url: "http://staging-api.clockin.com/api"
    mock_responses: false

# Scripts de test
scripts:
  all_tests: "flutter test"
  unit_only: "flutter test test/unit/"
  widget_only: "flutter test test/widget/"
  integration_only: "flutter test test/integration/"
  coverage: "flutter test --coverage"
  
  # Scripts de nettoyage
  clean_coverage: "rm -rf coverage/"
  clean_mocks: "rm -rf test/mocks/"

# Configuration CI/CD
ci_cd:
  enabled: true
  
  # Étapes de test dans la pipeline
  pipeline_steps:
    - name: "Setup Flutter"
      command: "flutter doctor"
    
    - name: "Get Dependencies"
      command: "flutter pub get"
    
    - name: "Analyze Code"
      command: "flutter analyze"
    
    - name: "Run Unit Tests"
      command: "flutter test test/unit/"
    
    - name: "Run Widget Tests"
      command: "flutter test test/widget/"
    
    - name: "Generate Coverage"
      command: "flutter test --coverage"
    
    - name: "Check Coverage Threshold"
      command: "genhtml coverage/lcov.info -o coverage/html"

# Notifications
notifications:
  enabled: true
  
  # Canaux de notification
  channels:
    slack:
      webhook_url: "${SLACK_WEBHOOK_URL}"
      on_failure: true
      on_success: false
    
    email:
      recipients:
        - "<EMAIL>"
      on_failure: true
      on_success: false

# Métriques et rapports
reporting:
  enabled: true
  
  # Types de rapports
  reports:
    test_results:
      format: "junit"
      output: "test-results.xml"
    
    coverage:
      format: "cobertura"
      output: "coverage.xml"
    
    performance:
      enabled: true
      thresholds:
        test_duration: 300  # 5 minutes max
        memory_usage: 512   # 512MB max
