import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/attendance_model.dart';
import '../../core/constants/app_constants.dart';
import '../../core/config/app_config.dart';
import '../../core/config/api_endpoints.dart';

/// Service pour les fonctionnalités administrateur
class AdminService {
  
  /// Récupère le token d'authentification
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.tokenKey);
  }

  /// Récupère la liste des employés
  Future<Map<String, dynamic>> getEmployees({
    int page = 1,
    int perPage = 20,
    String? search,
    String? department,
    String? role,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final Map<String, String> queryParams = {
        'page': page.toString(),
        'per_page': perPage.toString(),
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (department != null && department.isNotEmpty) {
        queryParams['department'] = department;
      }
      if (role != null && role.isNotEmpty) {
        queryParams['role'] = role;
      }

      final uri = Uri.parse('${AppConfig.baseUrl}${ApiEndpoints.employees}')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'employees': data['data'],
          'pagination': data['meta'] ?? data['pagination'],
          'message': 'Employés récupérés avec succès'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors de la récupération des employés: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  /// Récupère les détails d'un employé
  Future<Map<String, dynamic>> getEmployeeDetails(int employeeId) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}${ApiEndpoints.buildUrlWithId(ApiEndpoints.employeeDetails, employeeId)}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'employee': data['employee'],
          'message': 'Détails de l\'employé récupérés avec succès'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors de la récupération des détails: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  /// Crée un nouvel employé
  Future<Map<String, dynamic>> createEmployee(Map<String, dynamic> employeeData) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}${ApiEndpoints.createEmployee}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(employeeData),
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'employee': data['employee'],
          'message': 'Employé créé avec succès'
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de la création de l\'employé',
          'errors': data['errors'],
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  /// Met à jour un employé
  Future<Map<String, dynamic>> updateEmployee(int employeeId, Map<String, dynamic> employeeData) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.put(
        Uri.parse('${AppConfig.baseUrl}${ApiEndpoints.buildUrlWithId(ApiEndpoints.updateEmployee, employeeId)}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(employeeData),
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'employee': data['employee'],
          'message': 'Employé mis à jour avec succès'
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de la mise à jour de l\'employé',
          'errors': data['errors'],
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  /// Supprime un employé
  Future<Map<String, dynamic>> deleteEmployee(int employeeId) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.delete(
        Uri.parse('${AppConfig.baseUrl}${ApiEndpoints.buildUrlWithId(ApiEndpoints.deleteEmployee, employeeId)}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': 'Employé supprimé avec succès'
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['message'] ?? 'Erreur lors de la suppression de l\'employé'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  /// Récupère les présences d'un employé
  Future<Map<String, dynamic>> getEmployeeAttendance(
    int employeeId, {
    DateTime? startDate,
    DateTime? endDate,
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final Map<String, String> queryParams = {
        'page': page.toString(),
        'per_page': perPage.toString(),
      };

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      final uri = Uri.parse('${AppConfig.baseUrl}${ApiEndpoints.buildUrlWithIdAndResource(ApiEndpoints.employeeAttendance, employeeId, 'attendance')}')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'attendance': data['data'],
          'pagination': data['meta'] ?? data['pagination'],
          'message': 'Présences de l\'employé récupérées avec succès'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors de la récupération des présences: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  /// Récupère les statistiques du tableau de bord
  Future<Map<String, dynamic>> getDashboardStats() async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}${ApiEndpoints.dashboardStats}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'stats': data['stats'],
          'message': 'Statistiques récupérées avec succès'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors de la récupération des statistiques: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }
}
