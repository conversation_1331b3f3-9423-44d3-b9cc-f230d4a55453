import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/data/services/http_service.dart';
import 'package:clockin_mobile/data/services/auth_service.dart';
import 'package:clockin_mobile/data/services/attendance_service.dart';
import 'package:clockin_mobile/core/config/app_config.dart';
import 'package:clockin_mobile/core/config/api_endpoints.dart';

/// Tests d'intégration pour valider la communication avec le backend Laravel
void main() {
  group('Backend Integration Tests', () {
    late HttpService httpService;
    late AuthService authService;
    late AttendanceService attendanceService;

    setUpAll(() {
      // Configurer l'environnement de test
      AppConfig.setEnvironment(Environment.development);
      
      httpService = HttpService();
      authService = AuthService();
      attendanceService = AttendanceService();
    });

    tearDownAll(() {
      httpService.dispose();
    });

    group('API Health Check', () {
      test('should connect to backend API', () async {
        try {
          final isConnected = await httpService.checkConnectivity();
          expect(isConnected, isTrue, reason: 'Backend API should be accessible');
        } catch (e) {
          fail('Failed to connect to backend: $e');
        }
      });

      test('should get API version', () async {
        try {
          final response = await httpService.get(ApiEndpoints.version);
          expect(response, isNotNull);
          expect(response['version'], isNotNull);
        } catch (e) {
          // Version endpoint might not exist, this is optional
          print('Version endpoint not available: $e');
        }
      });
    });

    group('Authentication Integration', () {
      const testEmail = '<EMAIL>';
      const testPassword = 'password123';



      test('should handle user login', () async {
        try {
          final result = await authService.login(testEmail, testPassword);
          
          if (result['success'] == true) {
            expect(result['user'], isNotNull);
            expect(result['token'], isNotNull);
            expect(result['user']['email'], equals(testEmail));
            
            // Stocker le token pour les tests suivants
            httpService.setAuthToken(result['token']);
          } else {
            fail('Login should succeed with valid credentials: ${result['message']}');
          }
        } catch (e) {
          fail('Login test failed: $e');
        }
      });

      test('should handle invalid login', () async {
        try {
          final result = await authService.login('<EMAIL>', 'wrongpassword');
          expect(result['success'], isFalse);
          expect(result['message'], isNotNull);
        } catch (e) {
          // Une exception est acceptable pour des identifiants invalides
          expect(e.toString(), contains('401'));
        }
      });

      test('should get current user profile', () async {
        try {
          // S'assurer qu'on est connecté
          final loginResult = await authService.login(testEmail, testPassword);
          if (loginResult['success'] == true) {
            httpService.setAuthToken(loginResult['token']);
            
            final user = await authService.getCurrentUser();
            expect(user, isNotNull);
            expect(user!.email, equals(testEmail));
          }
        } catch (e) {
          print('Profile test skipped (not authenticated): $e');
        }
      });

      test('should handle logout', () async {
        try {
          final result = await authService.logout();
          expect(result['success'], isTrue);
        } catch (e) {
          // Logout peut échouer si pas connecté, c'est acceptable
          print('Logout test completed with exception: $e');
        }
      });
    });

    group('Attendance Integration', () {
      const testLatitude = 48.8566;
      const testLongitude = 2.3522;
      const testLocation = 'Paris, France';

      setUp(() async {
        // S'assurer qu'on est connecté pour les tests d'attendance
        try {
          final loginResult = await authService.login('<EMAIL>', 'password123');
          if (loginResult['success'] == true) {
            httpService.setAuthToken(loginResult['token']);
          }
        } catch (e) {
          print('Setup login failed: $e');
        }
      });

      test('should get today attendance', () async {
        try {
          final result = await attendanceService.getTodayAttendance();
          expect(result['success'], isTrue);
          // L'attendance peut être null si pas encore de check-in aujourd'hui
        } catch (e) {
          print('Today attendance test failed: $e');
        }
      });

      test('should handle check-in', () async {
        try {
          final result = await attendanceService.checkIn(
            latitude: testLatitude,
            longitude: testLongitude,
            location: testLocation,
            notes: 'Test check-in',
          );
          
          if (result['success'] == true) {
            expect(result['attendance'], isNotNull);
            expect(result['message'], isNotNull);
          } else {
            // Peut échouer si déjà check-in aujourd'hui
            print('Check-in failed (may be already checked in): ${result['message']}');
          }
        } catch (e) {
          print('Check-in test failed: $e');
        }
      });

      test('should get attendance history', () async {
        try {
          final result = await attendanceService.getAttendanceHistory(
            startDate: DateTime.now().subtract(const Duration(days: 30)),
            endDate: DateTime.now(),
          );
          
          expect(result['success'], isTrue);
          expect(result['data'], isNotNull);
          expect(result['data'], isList);
        } catch (e) {
          print('Attendance history test failed: $e');
        }
      });

      test('should get attendance statistics', () async {
        try {
          final result = await attendanceService.getAttendanceStats(
            startDate: DateTime.now().subtract(const Duration(days: 30)),
            endDate: DateTime.now(),
          );
          
          expect(result['success'], isTrue);
          expect(result['stats'], isNotNull);
        } catch (e) {
          print('Attendance stats test failed: $e');
        }
      });

      test('should handle check-out', () async {
        try {
          final result = await attendanceService.checkOut(
            latitude: testLatitude,
            longitude: testLongitude,
            location: testLocation,
            notes: 'Test check-out',
          );
          
          if (result['success'] == true) {
            expect(result['attendance'], isNotNull);
            expect(result['message'], isNotNull);
          } else {
            // Peut échouer si pas encore check-in ou déjà check-out
            print('Check-out failed (may not be checked in): ${result['message']}');
          }
        } catch (e) {
          print('Check-out test failed: $e');
        }
      });
    });

    group('Error Handling', () {
      test('should handle network errors gracefully', () async {
        // Tester avec une URL invalide
        final invalidHttpService = HttpService();
        
        try {
          await invalidHttpService.get('/invalid-endpoint');
          fail('Should throw an exception for invalid endpoint');
        } catch (e) {
          expect(e.toString(), contains('404'));
        }
      });

      test('should handle authentication errors', () async {
        // Tester sans token d'authentification
        httpService.setAuthToken(null);
        
        try {
          await httpService.get(ApiEndpoints.profile);
          fail('Should throw an exception for unauthenticated request');
        } catch (e) {
          expect(e.toString(), contains('401'));
        }
      });

      test('should handle invalid data', () async {
        try {
          final result = await authService.login('', '');
          expect(result['success'], isFalse);
          expect(result['message'], isNotNull);
        } catch (e) {
          // Une exception est acceptable pour des données invalides
          expect(e, isNotNull);
        }
      });
    });

    group('Data Validation', () {
      test('should validate API response structure', () async {
        try {
          final loginResult = await authService.login('<EMAIL>', 'password123');
          
          if (loginResult['success'] == true) {
            // Vérifier la structure de la réponse
            expect(loginResult, containsPair('success', true));
            expect(loginResult, contains('user'));
            expect(loginResult, contains('token'));
            expect(loginResult, contains('message'));
            
            final user = loginResult['user'];
            expect(user, contains('id'));
            expect(user, contains('name'));
            expect(user, contains('email'));
            expect(user, contains('role'));
          }
        } catch (e) {
          print('Response validation test skipped: $e');
        }
      });

      test('should validate attendance data structure', () async {
        try {
          final result = await attendanceService.getTodayAttendance();
          
          if (result['success'] == true && result['attendance'] != null) {
            final attendance = result['attendance'];
            expect(attendance, contains('id'));
            expect(attendance, contains('user_id'));
            expect(attendance, contains('date'));
            expect(attendance, contains('status'));
          }
        } catch (e) {
          print('Attendance validation test skipped: $e');
        }
      });
    });
  });
}
