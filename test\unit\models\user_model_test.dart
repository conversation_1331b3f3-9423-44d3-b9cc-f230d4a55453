import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/data/models/user_model.dart';

void main() {
  group('UserModel Tests', () {
    final testJson = {
      'id': 1,
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'phone': '+1234567890',
      'role': 'employee',
      'is_active': true,
      'avatar': 'https://example.com/avatar.jpg',
      'email_verified_at': '2023-01-15T10:00:00.000Z',
      'created_at': '2023-01-15T10:00:00.000Z',
      'updated_at': '2023-01-15T10:00:00.000Z',
    };

    final testUser = UserModel(
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890',
      role: 'employee',
      isActive: true,
      avatar: 'https://example.com/avatar.jpg',
      emailVerifiedAt: DateTime.parse('2023-01-15T10:00:00.000Z'),
      createdAt: DateTime.parse('2023-01-15T10:00:00.000Z'),
      updatedAt: DateTime.parse('2023-01-15T10:00:00.000Z'),
    );

    group('JSON Serialization', () {
      test('should create UserModel from JSON correctly', () {
        final user = UserModel.fromJson(testJson);

        expect(user.id, equals(1));
        expect(user.name, equals('John Doe'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.phone, equals('+1234567890'));
        expect(user.role, equals('employee'));
        expect(user.isActive, isTrue);
        expect(user.avatar, equals('https://example.com/avatar.jpg'));
        expect(user.emailVerifiedAt, equals(DateTime.parse('2023-01-15T10:00:00.000Z')));
        expect(user.createdAt, equals(DateTime.parse('2023-01-15T10:00:00.000Z')));
        expect(user.updatedAt, equals(DateTime.parse('2023-01-15T10:00:00.000Z')));
      });

      test('should convert UserModel to JSON correctly', () {
        final json = testUser.toJson();

        expect(json['id'], equals(1));
        expect(json['name'], equals('John Doe'));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['phone'], equals('+1234567890'));
        expect(json['role'], equals('employee'));
        expect(json['department'], equals('Engineering'));
        expect(json['position'], equals('Software Developer'));
        expect(json['employee_id'], equals('EMP001'));
        expect(json['hire_date'], equals('2023-01-15'));
        expect(json['is_active'], isTrue);
        expect(json['profile_picture'], equals('https://example.com/avatar.jpg'));
        expect(json['created_at'], equals('2023-01-15T10:00:00.000Z'));
        expect(json['updated_at'], equals('2023-01-15T10:00:00.000Z'));
      });

      test('should handle null optional fields in JSON', () {
        final jsonWithNulls = {
          'id': 1,
          'name': 'John Doe',
          'email': '<EMAIL>',
          'role': 'employee',
          'is_active': true,
          'created_at': '2023-01-15T10:00:00.000Z',
          'updated_at': '2023-01-15T10:00:00.000Z',
          // Optional fields are null or missing
          'phone': null,
          'department': null,
          'position': null,
          'employee_id': null,
          'hire_date': null,
          'profile_picture': null,
        };

        final user = UserModel.fromJson(jsonWithNulls);

        expect(user.id, equals(1));
        expect(user.name, equals('John Doe'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.role, equals('employee'));
        expect(user.isActive, isTrue);
        expect(user.phone, isNull);
        expect(user.avatar, isNull);
        expect(user.emailVerifiedAt, isNull);
      });
    });

    group('Equality and Props', () {
      test('should be equal when all properties are the same', () {
        final user1 = UserModel.fromJson(testJson);
        final user2 = UserModel.fromJson(testJson);

        expect(user1, equals(user2));
        expect(user1.hashCode, equals(user2.hashCode));
      });

      test('should not be equal when properties differ', () {
        final user1 = UserModel.fromJson(testJson);
        final modifiedJson = Map<String, dynamic>.from(testJson);
        modifiedJson['name'] = 'Jane Doe';
        final user2 = UserModel.fromJson(modifiedJson);

        expect(user1, isNot(equals(user2)));
        expect(user1.hashCode, isNot(equals(user2.hashCode)));
      });

      test('should have correct props for Equatable', () {
        final user = UserModel.fromJson(testJson);
        final props = user.props;

        expect(props, contains(user.id));
        expect(props, contains(user.name));
        expect(props, contains(user.email));
        expect(props, contains(user.phone));
        expect(props, contains(user.role));
        expect(props, contains(user.isActive));
        expect(props, contains(user.avatar));
        expect(props, contains(user.emailVerifiedAt));
        expect(props, contains(user.createdAt));
        expect(props, contains(user.updatedAt));
      });
    });

    group('Edge Cases', () {
      test('should handle empty strings in JSON', () {
        final jsonWithEmptyStrings = Map<String, dynamic>.from(testJson);
        jsonWithEmptyStrings['phone'] = '';
        jsonWithEmptyStrings['avatar'] = '';

        final user = UserModel.fromJson(jsonWithEmptyStrings);

        expect(user.phone, equals(''));
        expect(user.avatar, equals(''));
      });

      test('should handle different date formats', () {
        final jsonWithDifferentDate = Map<String, dynamic>.from(testJson);
        jsonWithDifferentDate['email_verified_at'] = '2023-01-15T00:00:00.000Z';

        final user = UserModel.fromJson(jsonWithDifferentDate);

        expect(user.emailVerifiedAt, isNotNull);
        expect(user.emailVerifiedAt!.year, equals(2023));
        expect(user.emailVerifiedAt!.month, equals(1));
        expect(user.emailVerifiedAt!.day, equals(15));
      });

      test('should handle boolean values correctly', () {
        final jsonWithFalse = Map<String, dynamic>.from(testJson);
        jsonWithFalse['is_active'] = false;

        final user = UserModel.fromJson(jsonWithFalse);

        expect(user.isActive, isFalse);
      });
    });

    group('String Representation', () {
      test('should have meaningful toString', () {
        final user = UserModel.fromJson(testJson);
        final stringRep = user.toString();

        expect(stringRep, contains('UserModel'));
        expect(stringRep, contains('John Doe'));
        expect(stringRep, contains('<EMAIL>'));
      });
    });

    group('Validation', () {
      test('should create valid user with required fields only', () {
        final minimalJson = {
          'id': 1,
          'name': 'John Doe',
          'email': '<EMAIL>',
          'role': 'employee',
          'is_active': true,
          'created_at': '2023-01-15T10:00:00.000Z',
          'updated_at': '2023-01-15T10:00:00.000Z',
        };

        expect(() => UserModel.fromJson(minimalJson), returnsNormally);
        
        final user = UserModel.fromJson(minimalJson);
        expect(user.id, equals(1));
        expect(user.name, equals('John Doe'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.role, equals('employee'));
        expect(user.isActive, isTrue);
      });
    });
  });
}
