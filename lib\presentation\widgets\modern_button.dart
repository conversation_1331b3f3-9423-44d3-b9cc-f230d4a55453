import 'package:flutter/material.dart';
import '../../core/constants/app_constants.dart';

/// Bouton moderne avec animations et effets visuels
class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool isLoading;
  final bool isOutlined;
  final double elevation;
  final TextStyle? textStyle;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.isLoading = false,
    this.isOutlined = false,
    this.elevation = 2.0,
    this.textStyle,
  });

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: isEnabled ? _handleTapDown : null,
            onTapUp: isEnabled ? _handleTapUp : null,
            onTapCancel: isEnabled ? _handleTapCancel : null,
            child: SizedBox(
              width: widget.width,
              height: widget.height ?? AppConstants.buttonHeight,
              child: widget.isOutlined
                  ? _buildOutlinedButton(theme, isEnabled)
                  : _buildElevatedButton(theme, isEnabled),
            ),
          ),
        );
      },
    );
  }

  Widget _buildElevatedButton(ThemeData theme, bool isEnabled) {
    return ElevatedButton(
      onPressed: isEnabled ? widget.onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: widget.backgroundColor ?? theme.colorScheme.primary,
        foregroundColor: widget.foregroundColor ?? theme.colorScheme.onPrimary,
        elevation: widget.elevation,
        padding: widget.padding ?? 
            const EdgeInsets.symmetric(horizontal: AppConstants.spacingL),
        shape: RoundedRectangleBorder(
          borderRadius: widget.borderRadius ?? 
              BorderRadius.circular(AppConstants.borderRadius),
        ),
        textStyle: widget.textStyle,
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlinedButton(ThemeData theme, bool isEnabled) {
    return OutlinedButton(
      onPressed: isEnabled ? widget.onPressed : null,
      style: OutlinedButton.styleFrom(
        foregroundColor: widget.foregroundColor ?? theme.colorScheme.primary,
        side: BorderSide(
          color: widget.backgroundColor ?? theme.colorScheme.primary,
          width: 2,
        ),
        padding: widget.padding ?? 
            const EdgeInsets.symmetric(horizontal: AppConstants.spacingL),
        shape: RoundedRectangleBorder(
          borderRadius: widget.borderRadius ?? 
              BorderRadius.circular(AppConstants.borderRadius),
        ),
        textStyle: widget.textStyle,
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (widget.isLoading) {
      return SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.foregroundColor ?? Colors.white,
          ),
        ),
      );
    }

    if (widget.icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(widget.icon, size: AppConstants.iconSize),
          const SizedBox(width: AppConstants.spacingS),
          Text(widget.text),
        ],
      );
    }

    return Text(widget.text);
  }
}

/// Bouton flottant moderne avec animations
class ModernFloatingButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String? tooltip;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double size;
  final bool isExtended;
  final String? label;

  const ModernFloatingButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.size = 56.0,
    this.isExtended = false,
    this.label,
  });

  @override
  State<ModernFloatingButton> createState() => _ModernFloatingButtonState();
}

class _ModernFloatingButtonState extends State<ModernFloatingButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTap() {
    _animationController.forward().then((_) {
      _animationController.reverse();
    });
    widget.onPressed?.call();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: widget.isExtended && widget.label != null
                ? FloatingActionButton.extended(
                    onPressed: _handleTap,
                    icon: Icon(widget.icon),
                    label: Text(widget.label!),
                    backgroundColor: widget.backgroundColor ?? theme.colorScheme.primary,
                    foregroundColor: widget.foregroundColor ?? theme.colorScheme.onPrimary,
                    tooltip: widget.tooltip,
                  )
                : FloatingActionButton(
                    onPressed: _handleTap,
                    backgroundColor: widget.backgroundColor ?? theme.colorScheme.primary,
                    foregroundColor: widget.foregroundColor ?? theme.colorScheme.onPrimary,
                    tooltip: widget.tooltip,
                    child: Icon(widget.icon),
                  ),
          ),
        );
      },
    );
  }
}

/// Bouton avec icône et texte en colonne
class IconTextButton extends StatelessWidget {
  final IconData icon;
  final String text;
  final VoidCallback? onPressed;
  final Color? color;
  final double iconSize;

  const IconTextButton({
    super.key,
    required this.icon,
    required this.text,
    this.onPressed,
    this.color,
    this.iconSize = AppConstants.iconSizeL,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonColor = color ?? theme.colorScheme.primary;

    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.spacingM),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: buttonColor,
            ),
            const SizedBox(height: AppConstants.spacingS),
            Text(
              text,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: buttonColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
