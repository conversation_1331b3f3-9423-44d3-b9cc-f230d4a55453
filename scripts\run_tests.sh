#!/bin/bash

# Script d'automatisation des tests pour ClockIn Mobile
# Usage: ./scripts/run_tests.sh [type] [options]

set -e  # Arrêter le script en cas d'erreur

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonctions utilitaires
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Vérifier que Flutter est installé
check_flutter() {
    if ! command -v flutter &> /dev/null; then
        print_error "Flutter n'est pas installé ou n'est pas dans le PATH"
        exit 1
    fi
    
    print_info "Version Flutter: $(flutter --version | head -n 1)"
}

# Nettoyer les fichiers de test précédents
clean_test_files() {
    print_info "Nettoyage des fichiers de test précédents..."
    
    if [ -d "coverage" ]; then
        rm -rf coverage/
        print_success "Dossier coverage supprimé"
    fi
    
    if [ -f "test-results.xml" ]; then
        rm test-results.xml
        print_success "Fichier test-results.xml supprimé"
    fi
}

# Installer les dépendances
install_dependencies() {
    print_info "Installation des dépendances..."
    flutter pub get
    print_success "Dépendances installées"
}

# Analyser le code
analyze_code() {
    print_header "ANALYSE DU CODE"
    
    print_info "Analyse statique du code..."
    if flutter analyze; then
        print_success "Analyse du code réussie"
    else
        print_error "Erreurs détectées lors de l'analyse"
        return 1
    fi
}

# Exécuter les tests unitaires
run_unit_tests() {
    print_header "TESTS UNITAIRES"
    
    if [ -d "test/unit" ]; then
        print_info "Exécution des tests unitaires..."
        if flutter test test/unit/; then
            print_success "Tests unitaires réussis"
        else
            print_error "Échec des tests unitaires"
            return 1
        fi
    else
        print_warning "Aucun test unitaire trouvé"
    fi
}

# Exécuter les tests de widgets
run_widget_tests() {
    print_header "TESTS DE WIDGETS"
    
    if [ -d "test/widget" ]; then
        print_info "Exécution des tests de widgets..."
        if flutter test test/widget/; then
            print_success "Tests de widgets réussis"
        else
            print_error "Échec des tests de widgets"
            return 1
        fi
    else
        print_warning "Aucun test de widget trouvé"
    fi
}

# Exécuter les tests d'intégration
run_integration_tests() {
    print_header "TESTS D'INTÉGRATION"
    
    if [ -d "test/integration" ]; then
        print_info "Exécution des tests d'intégration..."
        if flutter test test/integration/; then
            print_success "Tests d'intégration réussis"
        else
            print_error "Échec des tests d'intégration"
            return 1
        fi
    else
        print_warning "Aucun test d'intégration trouvé"
    fi
}

# Générer la couverture de code
generate_coverage() {
    print_header "COUVERTURE DE CODE"
    
    print_info "Génération de la couverture de code..."
    if flutter test --coverage; then
        print_success "Couverture de code générée"
        
        # Générer le rapport HTML si lcov est disponible
        if command -v genhtml &> /dev/null; then
            print_info "Génération du rapport HTML..."
            genhtml coverage/lcov.info -o coverage/html
            print_success "Rapport HTML généré dans coverage/html/"
        else
            print_warning "genhtml non disponible, rapport HTML non généré"
        fi
        
        # Afficher un résumé de la couverture
        if command -v lcov &> /dev/null; then
            print_info "Résumé de la couverture:"
            lcov --summary coverage/lcov.info
        fi
    else
        print_error "Échec de la génération de la couverture"
        return 1
    fi
}

# Exécuter tous les tests
run_all_tests() {
    print_header "EXÉCUTION DE TOUS LES TESTS"
    
    analyze_code || exit 1
    run_unit_tests || exit 1
    run_widget_tests || exit 1
    run_integration_tests || exit 1
    generate_coverage || exit 1
    
    print_success "Tous les tests ont réussi !"
}

# Afficher l'aide
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "COMMANDS:"
    echo "  all           Exécuter tous les tests (par défaut)"
    echo "  unit          Exécuter uniquement les tests unitaires"
    echo "  widget        Exécuter uniquement les tests de widgets"
    echo "  integration   Exécuter uniquement les tests d'intégration"
    echo "  coverage      Générer uniquement la couverture de code"
    echo "  analyze       Analyser uniquement le code"
    echo "  clean         Nettoyer les fichiers de test"
    echo "  help          Afficher cette aide"
    echo ""
    echo "OPTIONS:"
    echo "  --no-clean    Ne pas nettoyer avant les tests"
    echo "  --verbose     Affichage détaillé"
    echo ""
    echo "EXEMPLES:"
    echo "  $0                    # Exécuter tous les tests"
    echo "  $0 unit              # Tests unitaires seulement"
    echo "  $0 coverage          # Couverture de code seulement"
    echo "  $0 all --no-clean    # Tous les tests sans nettoyage"
}

# Fonction principale
main() {
    local command="${1:-all}"
    local no_clean=false
    local verbose=false
    
    # Parser les options
    while [[ $# -gt 0 ]]; do
        case $1 in
            --no-clean)
                no_clean=true
                shift
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            *)
                shift
                ;;
        esac
    done
    
    # Vérifications initiales
    check_flutter
    
    # Nettoyage conditionnel
    if [ "$no_clean" = false ]; then
        clean_test_files
    fi
    
    # Installation des dépendances
    install_dependencies
    
    # Exécution selon la commande
    case $command in
        all)
            run_all_tests
            ;;
        unit)
            analyze_code && run_unit_tests
            ;;
        widget)
            analyze_code && run_widget_tests
            ;;
        integration)
            analyze_code && run_integration_tests
            ;;
        coverage)
            generate_coverage
            ;;
        analyze)
            analyze_code
            ;;
        clean)
            clean_test_files
            ;;
        help)
            show_help
            ;;
        *)
            print_error "Commande inconnue: $command"
            show_help
            exit 1
            ;;
    esac
}

# Point d'entrée
main "$@"
