import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../data/repositories/auth_repository.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tableau de bord'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.access_time, size: 64),
            <PERSON><PERSON><PERSON><PERSON>(height: 16),
            Text(
              'Bienvenue sur ClockIn',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON>ox(height: 8),
            Text('Votre application de pointage'),
          ],
        ),
      ),
    );
  }

  void _logout(BuildContext context) {
    context.read<AuthRepository>().logout();
    Navigator.pushReplacementNamed(context, '/login');
  }
}


