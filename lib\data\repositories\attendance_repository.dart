import 'package:flutter/foundation.dart';
import '../models/attendance_model.dart';
import '../services/attendance_service.dart';
import '../services/location_service.dart';

class AttendanceRepository extends ChangeNotifier {
  final AttendanceService _attendanceService = AttendanceService();
  final LocationService _locationService = LocationService();

  List<AttendanceModel> _attendanceHistory = [];
  bool _isLoading = false;
  String? _error;
  AttendanceModel? _todayAttendance;
  bool _isCheckedIn = false;

  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get errorMessage => _error;
  AttendanceModel? get todayAttendance => _todayAttendance;
  bool get isCheckedIn => _isCheckedIn;
  List<AttendanceModel> get attendanceHistory => _attendanceHistory;

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  Future<bool> checkIn({String? notes}) async {
    _setLoading(true);
    _clearError();

    try {
      final locationResult = await _locationService.getCurrentPosition();
      
      if (!locationResult['success']) {
        _setError(locationResult['message']);
        return false;
      }

      final result = await _attendanceService.checkIn(
        latitude: locationResult['latitude'],
        longitude: locationResult['longitude'],
        location: locationResult['location'],
        notes: notes,
      );
      
      if (result['success']) {
        _todayAttendance = AttendanceModel.fromJson(result['attendance']);
        _isCheckedIn = true;
        notifyListeners();
        return true;
      } else {
        _setError(result['message']);
        return false;
      }
    } catch (e) {
      _setError('Check-in échoué: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> checkOut({String? notes}) async {
    _setLoading(true);
    _clearError();

    try {
      final locationResult = await _locationService.getCurrentPosition();
      
      if (!locationResult['success']) {
        _setError(locationResult['message']);
        return false;
      }

      final result = await _attendanceService.checkOut(
        latitude: locationResult['latitude'],
        longitude: locationResult['longitude'],
        location: locationResult['location'],
        notes: notes,
      );
      
      if (result['success']) {
        _todayAttendance = AttendanceModel.fromJson(result['attendance']);
        _isCheckedIn = false;
        notifyListeners();
        return true;
      } else {
        _setError(result['message']);
        return false;
      }
    } catch (e) {
      _setError('Check-out échoué: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Get attendance history
  Future<void> getAttendanceHistory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _attendanceService.getAttendanceHistory(
        startDate: startDate,
        endDate: endDate,
      );

      if (result['success']) {
        final List<dynamic> attendanceList = result['data'] ?? [];
        _attendanceHistory = attendanceList
            .map((json) => AttendanceModel.fromJson(json))
            .toList();
        notifyListeners();
      } else {
        _setError(result['message']);
      }
    } catch (e) {
      _setError('Failed to get attendance history: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Get today's attendance status
  Future<void> getTodayAttendance() async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _attendanceService.getTodayAttendance();

      if (result['success']) {
        if (result['attendance'] != null) {
          _todayAttendance = AttendanceModel.fromJson(result['attendance']);
          _isCheckedIn = _todayAttendance!.checkinTime != null &&
                        _todayAttendance!.checkoutTime == null;
        } else {
          _todayAttendance = null;
          _isCheckedIn = false;
        }
        notifyListeners();
      } else {
        _setError(result['message']);
      }
    } catch (e) {
      _setError('Failed to get today\'s attendance: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }
}

