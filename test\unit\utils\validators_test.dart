import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/core/utils/validators.dart';

void main() {
  group('Validators Tests', () {
    group('Email Validation', () {
      test('should return null for valid email', () {
        expect(Validators.email('<EMAIL>'), isNull);
        expect(Validators.email('<EMAIL>'), isNull);
        expect(Validators.email('<EMAIL>'), isNull);
      });

      test('should return error for invalid email', () {
        expect(Validators.email('invalid-email'), isNotNull);
        expect(Validators.email('test@'), isNotNull);
        expect(Validators.email('@domain.com'), isNotNull);
        expect(Validators.email('test.domain.com'), isNotNull);
      });

      test('should return error for empty email', () {
        expect(Validators.email(''), isNotNull);
        expect(Validators.email(null), isNotNull);
      });
    });

    group('Password Validation', () {
      test('should return null for valid password', () {
        expect(Validators.password('password123'), isNull);
        expect(Validators.password('123456'), isNull);
        expect(Validators.password('verylongpassword'), isNull);
      });

      test('should return error for short password', () {
        expect(Validators.password('12345'), isNotNull);
        expect(Validators.password('abc'), isNotNull);
      });

      test('should return error for empty password', () {
        expect(Validators.password(''), isNotNull);
        expect(Validators.password(null), isNotNull);
      });
    });

    group('Required Field Validation', () {
      test('should return null for non-empty value', () {
        expect(Validators.required('test'), isNull);
        expect(Validators.required('123'), isNull);
        expect(Validators.required('a'), isNull);
      });

      test('should return error for empty value', () {
        expect(Validators.required(''), isNotNull);
        expect(Validators.required('   '), isNotNull);
        expect(Validators.required(null), isNotNull);
      });

      test('should use custom field name in error message', () {
        final error = Validators.required('', 'Username');
        expect(error, contains('Username'));
      });
    });

    group('Phone Validation', () {
      test('should return null for valid phone numbers', () {
        expect(Validators.phone('+1234567890'), isNull);
        expect(Validators.phone('1234567890'), isNull);
        expect(Validators.phone('+33123456789'), isNull);
      });

      test('should return error for invalid phone numbers', () {
        expect(Validators.phone('1'), isNotNull); // Too short
        expect(Validators.phone('abc123'), isNotNull); // Contains letters
        expect(Validators.phone('+'), isNotNull); // Just plus sign
        expect(Validators.phone('0123456789'), isNotNull); // Starts with 0
        expect(Validators.phone('123456789012345678'), isNotNull); // Too long
      });

      test('should return error for empty phone', () {
        expect(Validators.phone(''), isNotNull);
        expect(Validators.phone(null), isNotNull);
      });
    });

    group('Minimum Length Validation', () {
      test('should return null for valid length', () {
        expect(Validators.minLength('12345', 5), isNull);
        expect(Validators.minLength('123456', 5), isNull);
      });

      test('should return error for short length', () {
        expect(Validators.minLength('1234', 5), isNotNull);
        expect(Validators.minLength('', 1), isNotNull);
      });
    });

    group('Maximum Length Validation', () {
      test('should return null for valid length', () {
        expect(Validators.maxLength('12345', 5), isNull);
        expect(Validators.maxLength('1234', 5), isNull);
        expect(Validators.maxLength(null, 5), isNull);
      });

      test('should return error for long length', () {
        expect(Validators.maxLength('123456', 5), isNotNull);
      });
    });

    group('Numeric Validation', () {
      test('should return null for valid numbers', () {
        expect(Validators.numeric('123'), isNull);
        expect(Validators.numeric('123.45'), isNull);
        expect(Validators.numeric('-123'), isNull);
      });

      test('should return error for non-numeric values', () {
        expect(Validators.numeric('abc'), isNotNull);
        expect(Validators.numeric('12a3'), isNotNull);
      });

      test('should return error for empty value', () {
        expect(Validators.numeric(''), isNotNull);
        expect(Validators.numeric(null), isNotNull);
      });
    });

    group('Confirm Password Validation', () {
      test('should return null for matching passwords', () {
        expect(Validators.confirmPassword('password', 'password'), isNull);
      });

      test('should return error for non-matching passwords', () {
        expect(Validators.confirmPassword('password1', 'password2'), isNotNull);
      });

      test('should return error for empty confirmation', () {
        expect(Validators.confirmPassword('', 'password'), isNotNull);
        expect(Validators.confirmPassword(null, 'password'), isNotNull);
      });
    });

    group('URL Validation', () {
      test('should return null for valid URLs', () {
        expect(Validators.url('https://example.com'), isNull);
        expect(Validators.url('http://test.co.uk'), isNull);
        expect(Validators.url('https://www.google.com/search?q=test'), isNull);
      });

      test('should return error for invalid URLs', () {
        expect(Validators.url('not-a-url'), isNotNull);
        expect(Validators.url('ftp://example.com'), isNotNull);
        expect(Validators.url('example.com'), isNotNull);
      });

      test('should return error for empty URL', () {
        expect(Validators.url(''), isNotNull);
        expect(Validators.url(null), isNotNull);
      });
    });

    group('Combined Validators', () {
      test('should combine multiple validators correctly', () {
        final validator = Validators.combine([
          Validators.required,
          (value) => Validators.minLength(value, 3),
        ]);

        expect(validator('test'), isNull);
        expect(validator(''), isNotNull);
        expect(validator('ab'), isNotNull);
      });

      test('should return first error from combined validators', () {
        final validator = Validators.combine([
          Validators.required,
          (value) => Validators.email(value),
        ]);

        final error = validator('');
        expect(error, contains('required'));
      });
    });
  });
}
