import 'package:equatable/equatable.dart';

class AttendanceModel extends Equatable {
  final int id;
  final int userId;
  final DateTime date;
  final DateTime? checkinTime;
  final DateTime? checkoutTime;
  final String? checkinLocation;
  final String? checkoutLocation;
  final double? checkinLatitude;
  final double? checkinLongitude;
  final double? checkoutLatitude;
  final double? checkoutLongitude;
  final String status;
  final String? notes;
  final Duration? totalHours;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AttendanceModel({
    required this.id,
    required this.userId,
    required this.date,
    this.checkinTime,
    this.checkoutTime,
    this.checkinLocation,
    this.checkoutLocation,
    this.checkinLatitude,
    this.checkinLongitude,
    this.checkoutLatitude,
    this.checkoutLongitude,
    required this.status,
    this.notes,
    this.totalHours,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AttendanceModel.fromJson(Map<String, dynamic> json) {
    return AttendanceModel(
      id: json['id'] as int,
      userId: json['user_id'] as int,
      date: DateTime.parse(json['date'] as String),
      checkinTime: json['checkin_time'] != null
          ? DateTime.parse(json['checkin_time'] as String)
          : null,
      checkoutTime: json['checkout_time'] != null
          ? DateTime.parse(json['checkout_time'] as String)
          : null,
      checkinLocation: json['checkin_location'] as String?,
      checkoutLocation: json['checkout_location'] as String?,
      checkinLatitude: json['checkin_latitude'] != null
          ? (json['checkin_latitude'] as num).toDouble()
          : null,
      checkinLongitude: json['checkin_longitude'] != null
          ? (json['checkin_longitude'] as num).toDouble()
          : null,
      checkoutLatitude: json['checkout_latitude'] != null
          ? (json['checkout_latitude'] as num).toDouble()
          : null,
      checkoutLongitude: json['checkout_longitude'] != null
          ? (json['checkout_longitude'] as num).toDouble()
          : null,
      status: json['status'] as String,
      notes: json['notes'] as String?,
      totalHours: json['total_hours'] != null
          ? Duration(minutes: (json['total_hours'] as num).toInt())
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'date': date.toIso8601String().split('T')[0],
      'checkin_time': checkinTime?.toIso8601String(),
      'checkout_time': checkoutTime?.toIso8601String(),
      'checkin_location': checkinLocation,
      'checkout_location': checkoutLocation,
      'checkin_latitude': checkinLatitude,
      'checkin_longitude': checkinLongitude,
      'checkout_latitude': checkoutLatitude,
      'checkout_longitude': checkoutLongitude,
      'status': status,
      'notes': notes,
      'total_hours': totalHours?.inMinutes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  AttendanceModel copyWith({
    int? id,
    int? userId,
    DateTime? date,
    DateTime? checkinTime,
    DateTime? checkoutTime,
    String? checkinLocation,
    String? checkoutLocation,
    double? checkinLatitude,
    double? checkinLongitude,
    double? checkoutLatitude,
    double? checkoutLongitude,
    String? status,
    String? notes,
    Duration? totalHours,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AttendanceModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      date: date ?? this.date,
      checkinTime: checkinTime ?? this.checkinTime,
      checkoutTime: checkoutTime ?? this.checkoutTime,
      checkinLocation: checkinLocation ?? this.checkinLocation,
      checkoutLocation: checkoutLocation ?? this.checkoutLocation,
      checkinLatitude: checkinLatitude ?? this.checkinLatitude,
      checkinLongitude: checkinLongitude ?? this.checkinLongitude,
      checkoutLatitude: checkoutLatitude ?? this.checkoutLatitude,
      checkoutLongitude: checkoutLongitude ?? this.checkoutLongitude,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      totalHours: totalHours ?? this.totalHours,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        date,
        checkinTime,
        checkoutTime,
        checkinLocation,
        checkoutLocation,
        checkinLatitude,
        checkinLongitude,
        checkoutLatitude,
        checkoutLongitude,
        status,
        notes,
        totalHours,
        createdAt,
        updatedAt,
      ];

  /// Vérifie si l'employé est actuellement en service
  bool get isCheckedIn => checkinTime != null && checkoutTime == null;

  /// Vérifie si l'employé a terminé sa journée
  bool get isCheckedOut => checkinTime != null && checkoutTime != null;

  /// Calcule les heures travaillées
  Duration get workedHours {
    if (checkinTime == null) return Duration.zero;
    if (checkoutTime == null) return Duration.zero;
    return checkoutTime!.difference(checkinTime!);
  }

  /// Retourne les heures travaillées formatées
  String get formattedWorkedHours {
    final hours = workedHours;
    final h = hours.inHours;
    final m = hours.inMinutes.remainder(60);
    return '${h}h ${m}m';
  }

  /// Vérifie si c'est un jour de travail complet (8h)
  bool get isFullWorkDay => workedHours.inHours >= 8;

  /// Vérifie si c'est un retard (après 9h)
  bool get isLate {
    if (checkinTime == null) return false;
    final workStart = DateTime(date.year, date.month, date.day, 9, 0); // 9h00
    return checkinTime!.isAfter(workStart);
  }

  /// Vérifie si c'est un départ anticipé (avant 17h)
  bool get isEarlyLeave {
    if (checkoutTime == null) return false;
    final workEnd = DateTime(date.year, date.month, date.day, 17, 0); // 17h00
    return checkoutTime!.isBefore(workEnd);
  }

  /// Retourne le statut formaté
  String get formattedStatus {
    switch (status.toLowerCase()) {
      case 'present':
        return 'Présent';
      case 'absent':
        return 'Absent';
      case 'late':
        return 'En retard';
      case 'half_day':
        return 'Demi-journée';
      case 'sick':
        return 'Maladie';
      case 'vacation':
        return 'Congé';
      default:
        return status;
    }
  }

  /// Retourne la couleur associée au statut
  String get statusColor {
    switch (status.toLowerCase()) {
      case 'present':
        return '#4CAF50'; // Vert
      case 'absent':
        return '#F44336'; // Rouge
      case 'late':
        return '#FF9800'; // Orange
      case 'half_day':
        return '#2196F3'; // Bleu
      case 'sick':
        return '#9C27B0'; // Violet
      case 'vacation':
        return '#00BCD4'; // Cyan
      default:
        return '#757575'; // Gris
    }
  }

  @override
  String toString() {
    return 'AttendanceModel(id: $id, userId: $userId, date: $date, status: $status, workedHours: $formattedWorkedHours)';
  }
}

/// Énumération des statuts de présence
enum AttendanceStatus {
  present('present', 'Présent'),
  absent('absent', 'Absent'),
  late('late', 'En retard'),
  halfDay('half_day', 'Demi-journée'),
  sick('sick', 'Maladie'),
  vacation('vacation', 'Congé');

  const AttendanceStatus(this.value, this.displayName);

  final String value;
  final String displayName;

  static AttendanceStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'present':
        return AttendanceStatus.present;
      case 'absent':
        return AttendanceStatus.absent;
      case 'late':
        return AttendanceStatus.late;
      case 'half_day':
        return AttendanceStatus.halfDay;
      case 'sick':
        return AttendanceStatus.sick;
      case 'vacation':
        return AttendanceStatus.vacation;
      default:
        return AttendanceStatus.present;
    }
  }
}
