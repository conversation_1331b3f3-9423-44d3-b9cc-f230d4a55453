import 'package:equatable/equatable.dart';

/// Modèle générique pour les réponses API Laravel
class ApiResponse<T> extends Equatable {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? errors;
  final Map<String, dynamic>? meta;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
    this.meta,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] as bool? ?? true,
      message: json['message'] as String?,
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'] as T?,
      errors: json['errors'] as Map<String, dynamic>?,
      meta: json['meta'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'errors': errors,
      'meta': meta,
    };
  }

  @override
  List<Object?> get props => [success, message, data, errors, meta];

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, hasData: ${data != null})';
  }
}

/// Modèle pour les réponses paginées
class PaginatedResponse<T> extends Equatable {
  final List<T> data;
  final int currentPage;
  final int lastPage;
  final int perPage;
  final int total;
  final int from;
  final int to;
  final String? nextPageUrl;
  final String? prevPageUrl;

  const PaginatedResponse({
    required this.data,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
    required this.from,
    required this.to,
    this.nextPageUrl,
    this.prevPageUrl,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final dataList = json['data'] as List<dynamic>? ?? [];
    
    return PaginatedResponse<T>(
      data: dataList.map((item) => fromJsonT(item as Map<String, dynamic>)).toList(),
      currentPage: json['current_page'] as int? ?? 1,
      lastPage: json['last_page'] as int? ?? 1,
      perPage: json['per_page'] as int? ?? 15,
      total: json['total'] as int? ?? 0,
      from: json['from'] as int? ?? 0,
      to: json['to'] as int? ?? 0,
      nextPageUrl: json['next_page_url'] as String?,
      prevPageUrl: json['prev_page_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'current_page': currentPage,
      'last_page': lastPage,
      'per_page': perPage,
      'total': total,
      'from': from,
      'to': to,
      'next_page_url': nextPageUrl,
      'prev_page_url': prevPageUrl,
    };
  }

  /// Vérifie s'il y a une page suivante
  bool get hasNextPage => currentPage < lastPage;

  /// Vérifie s'il y a une page précédente
  bool get hasPrevPage => currentPage > 1;

  /// Vérifie si c'est la première page
  bool get isFirstPage => currentPage == 1;

  /// Vérifie si c'est la dernière page
  bool get isLastPage => currentPage == lastPage;

  @override
  List<Object?> get props => [
        data,
        currentPage,
        lastPage,
        perPage,
        total,
        from,
        to,
        nextPageUrl,
        prevPageUrl,
      ];

  @override
  String toString() {
    return 'PaginatedResponse(total: $total, currentPage: $currentPage, lastPage: $lastPage, itemsCount: ${data.length})';
  }
}

/// Modèle pour les erreurs de validation Laravel
class ValidationError extends Equatable {
  final String field;
  final List<String> messages;

  const ValidationError({
    required this.field,
    required this.messages,
  });

  factory ValidationError.fromJson(String field, dynamic messages) {
    List<String> messageList;
    if (messages is List) {
      messageList = messages.cast<String>();
    } else if (messages is String) {
      messageList = [messages];
    } else {
      messageList = ['Invalid field'];
    }

    return ValidationError(
      field: field,
      messages: messageList,
    );
  }

  /// Retourne le premier message d'erreur
  String get firstMessage => messages.isNotEmpty ? messages.first : 'Invalid field';

  /// Retourne tous les messages concaténés
  String get allMessages => messages.join(', ');

  @override
  List<Object?> get props => [field, messages];

  @override
  String toString() {
    return 'ValidationError(field: $field, messages: $messages)';
  }
}

/// Utilitaire pour parser les erreurs de validation Laravel
class ValidationErrorParser {
  static List<ValidationError> parseErrors(Map<String, dynamic>? errors) {
    if (errors == null) return [];

    return errors.entries
        .map((entry) => ValidationError.fromJson(entry.key, entry.value))
        .toList();
  }

  static String getFirstErrorMessage(Map<String, dynamic>? errors) {
    final validationErrors = parseErrors(errors);
    return validationErrors.isNotEmpty 
        ? validationErrors.first.firstMessage 
        : 'Validation error';
  }

  static Map<String, String> getErrorsMap(Map<String, dynamic>? errors) {
    final validationErrors = parseErrors(errors);
    return Map.fromEntries(
      validationErrors.map((error) => MapEntry(error.field, error.firstMessage))
    );
  }
}
