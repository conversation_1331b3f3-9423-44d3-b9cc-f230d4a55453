@echo off
REM Script d'automatisation des tests pour ClockIn Mobile (Windows)
REM Usage: scripts\run_tests.bat [type] [options]

setlocal enabledelayedexpansion

REM Variables
set "COMMAND=%1"
set "NO_CLEAN=false"
set "VERBOSE=false"

REM Couleurs (limitées sur Windows)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Fonction pour afficher les en-têtes
:print_header
echo %BLUE%================================%NC%
echo %BLUE%%~1%NC%
echo %BLUE%================================%NC%
goto :eof

REM Fonction pour afficher les succès
:print_success
echo %GREEN%✓ %~1%NC%
goto :eof

REM Fonction pour afficher les avertissements
:print_warning
echo %YELLOW%! %~1%NC%
goto :eof

REM Fonction pour afficher les erreurs
:print_error
echo %RED%✗ %~1%NC%
goto :eof

REM Fonction pour afficher les infos
:print_info
echo %BLUE%i %~1%NC%
goto :eof

REM Vérifier que Flutter est installé
:check_flutter
flutter --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Flutter n'est pas installé ou n'est pas dans le PATH"
    exit /b 1
)
call :print_info "Flutter détecté"
goto :eof

REM Nettoyer les fichiers de test précédents
:clean_test_files
call :print_info "Nettoyage des fichiers de test précédents..."

if exist "coverage" (
    rmdir /s /q "coverage"
    call :print_success "Dossier coverage supprimé"
)

if exist "test-results.xml" (
    del "test-results.xml"
    call :print_success "Fichier test-results.xml supprimé"
)
goto :eof

REM Installer les dépendances
:install_dependencies
call :print_info "Installation des dépendances..."
flutter pub get
if errorlevel 1 (
    call :print_error "Échec de l'installation des dépendances"
    exit /b 1
)
call :print_success "Dépendances installées"
goto :eof

REM Analyser le code
:analyze_code
call :print_header "ANALYSE DU CODE"
call :print_info "Analyse statique du code..."
flutter analyze
if errorlevel 1 (
    call :print_error "Erreurs détectées lors de l'analyse"
    exit /b 1
)
call :print_success "Analyse du code réussie"
goto :eof

REM Exécuter les tests unitaires
:run_unit_tests
call :print_header "TESTS UNITAIRES"

if exist "test\unit" (
    call :print_info "Exécution des tests unitaires..."
    flutter test test/unit/
    if errorlevel 1 (
        call :print_error "Échec des tests unitaires"
        exit /b 1
    )
    call :print_success "Tests unitaires réussis"
) else (
    call :print_warning "Aucun test unitaire trouvé"
)
goto :eof

REM Exécuter les tests de widgets
:run_widget_tests
call :print_header "TESTS DE WIDGETS"

if exist "test\widget" (
    call :print_info "Exécution des tests de widgets..."
    flutter test test/widget/
    if errorlevel 1 (
        call :print_error "Échec des tests de widgets"
        exit /b 1
    )
    call :print_success "Tests de widgets réussis"
) else (
    call :print_warning "Aucun test de widget trouvé"
)
goto :eof

REM Exécuter les tests d'intégration
:run_integration_tests
call :print_header "TESTS D'INTÉGRATION"

if exist "test\integration" (
    call :print_info "Exécution des tests d'intégration..."
    flutter test test/integration/
    if errorlevel 1 (
        call :print_error "Échec des tests d'intégration"
        exit /b 1
    )
    call :print_success "Tests d'intégration réussis"
) else (
    call :print_warning "Aucun test d'intégration trouvé"
)
goto :eof

REM Générer la couverture de code
:generate_coverage
call :print_header "COUVERTURE DE CODE"

call :print_info "Génération de la couverture de code..."
flutter test --coverage
if errorlevel 1 (
    call :print_error "Échec de la génération de la couverture"
    exit /b 1
)
call :print_success "Couverture de code générée"

REM Vérifier si genhtml est disponible (peu probable sur Windows)
where genhtml >nul 2>&1
if not errorlevel 1 (
    call :print_info "Génération du rapport HTML..."
    genhtml coverage/lcov.info -o coverage/html
    call :print_success "Rapport HTML généré dans coverage/html/"
) else (
    call :print_warning "genhtml non disponible, rapport HTML non généré"
    call :print_info "Vous pouvez utiliser un outil comme lcov-viewer pour visualiser la couverture"
)
goto :eof

REM Exécuter tous les tests
:run_all_tests
call :print_header "EXÉCUTION DE TOUS LES TESTS"

call :analyze_code
if errorlevel 1 exit /b 1

call :run_unit_tests
if errorlevel 1 exit /b 1

call :run_widget_tests
if errorlevel 1 exit /b 1

call :run_integration_tests
if errorlevel 1 exit /b 1

call :generate_coverage
if errorlevel 1 exit /b 1

call :print_success "Tous les tests ont réussi !"
goto :eof

REM Afficher l'aide
:show_help
echo Usage: %0 [COMMAND] [OPTIONS]
echo.
echo COMMANDS:
echo   all           Exécuter tous les tests (par défaut)
echo   unit          Exécuter uniquement les tests unitaires
echo   widget        Exécuter uniquement les tests de widgets
echo   integration   Exécuter uniquement les tests d'intégration
echo   coverage      Générer uniquement la couverture de code
echo   analyze       Analyser uniquement le code
echo   clean         Nettoyer les fichiers de test
echo   help          Afficher cette aide
echo.
echo OPTIONS:
echo   --no-clean    Ne pas nettoyer avant les tests
echo   --verbose     Affichage détaillé
echo.
echo EXEMPLES:
echo   %0                    # Exécuter tous les tests
echo   %0 unit              # Tests unitaires seulement
echo   %0 coverage          # Couverture de code seulement
echo   %0 all --no-clean    # Tous les tests sans nettoyage
goto :eof

REM Fonction principale
:main
REM Parser les arguments
:parse_args
if "%~2"=="--no-clean" set "NO_CLEAN=true"
if "%~2"=="--verbose" set "VERBOSE=true"
if "%~3"=="--no-clean" set "NO_CLEAN=true"
if "%~3"=="--verbose" set "VERBOSE=true"

REM Définir la commande par défaut
if "%COMMAND%"=="" set "COMMAND=all"

REM Vérifications initiales
call :check_flutter
if errorlevel 1 exit /b 1

REM Nettoyage conditionnel
if "%NO_CLEAN%"=="false" (
    call :clean_test_files
)

REM Installation des dépendances
call :install_dependencies
if errorlevel 1 exit /b 1

REM Exécution selon la commande
if "%COMMAND%"=="all" (
    call :run_all_tests
) else if "%COMMAND%"=="unit" (
    call :analyze_code && call :run_unit_tests
) else if "%COMMAND%"=="widget" (
    call :analyze_code && call :run_widget_tests
) else if "%COMMAND%"=="integration" (
    call :analyze_code && call :run_integration_tests
) else if "%COMMAND%"=="coverage" (
    call :generate_coverage
) else if "%COMMAND%"=="analyze" (
    call :analyze_code
) else if "%COMMAND%"=="clean" (
    call :clean_test_files
) else if "%COMMAND%"=="help" (
    call :show_help
) else (
    call :print_error "Commande inconnue: %COMMAND%"
    call :show_help
    exit /b 1
)

goto :eof

REM Point d'entrée
call :main %*
