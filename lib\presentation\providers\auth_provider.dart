import 'package:flutter/foundation.dart';
import '../../data/models/user_model.dart';
import '../../data/services/auth_service.dart';
import '../../core/utils/exceptions.dart';

/// États d'authentification
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Provider pour la gestion de l'authentification
class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  // État
  AuthState _state = AuthState.initial;
  UserModel? _user;
  String? _errorMessage;
  String? _token;

  // Getters
  AuthState get state => _state;
  UserModel? get user => _user;
  String? get errorMessage => _errorMessage;
  String? get token => _token;
  bool get isAuthenticated => _state == AuthState.authenticated && _user != null;
  bool get isLoading => _state == AuthState.loading;
  bool get hasError => _state == AuthState.error;

  /// Initialise le provider et vérifie l'état d'authentification
  Future<void> initialize() async {
    _setState(AuthState.loading);
    
    try {
      await _authService.initialize();
      final isAuth = await _authService.isAuthenticated();
      
      if (isAuth) {
        final currentUser = await _authService.getCurrentUser();
        final currentToken = await _authService.getToken();
        
        if (currentUser != null && currentToken != null) {
          _user = currentUser;
          _token = currentToken;
          _setState(AuthState.authenticated);
        } else {
          _setState(AuthState.unauthenticated);
        }
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      _setError('Erreur d\'initialisation: ${e.toString()}');
    }
  }

  /// Connexion utilisateur
  Future<bool> login(String email, String password) async {
    _setState(AuthState.loading);
    _clearError();

    try {
      final result = await _authService.login(email, password);
      
      if (result['success'] == true) {
        _user = result['user'] as UserModel;
        _token = result['token'] as String;
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(result['message'] ?? 'Erreur de connexion');
        return false;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erreur de connexion: ${e.toString()}');
      return false;
    }
  }

  /// Inscription utilisateur
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    String? phone,
  }) async {
    _setState(AuthState.loading);
    _clearError();

    try {
      final result = await _authService.register(
        name: name,
        email: email,
        password: password,
        passwordConfirmation: passwordConfirmation,
        phone: phone,
      );
      
      if (result['success'] == true) {
        _user = result['user'] as UserModel;
        _token = result['token'] as String;
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(result['message'] ?? 'Erreur d\'inscription');
        return false;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erreur d\'inscription: ${e.toString()}');
      return false;
    }
  }

  /// Déconnexion utilisateur
  Future<void> logout() async {
    _setState(AuthState.loading);
    
    try {
      await _authService.logout();
    } catch (e) {
      // Continue même en cas d'erreur pour nettoyer l'état local
      debugPrint('Erreur lors de la déconnexion: $e');
    } finally {
      _user = null;
      _token = null;
      _clearError();
      _setState(AuthState.unauthenticated);
    }
  }

  /// Met à jour le profil utilisateur
  Future<bool> updateProfile(Map<String, dynamic> data) async {
    if (!isAuthenticated) return false;

    _setState(AuthState.loading);
    _clearError();

    try {
      final result = await _authService.updateProfile(data);
      
      if (result['success'] == true) {
        _user = result['user'] as UserModel;
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(result['message'] ?? 'Erreur de mise à jour');
        return false;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erreur de mise à jour: ${e.toString()}');
      return false;
    }
  }

  /// Change le mot de passe
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
    required String newPasswordConfirmation,
  }) async {
    if (!isAuthenticated) return false;

    _setState(AuthState.loading);
    _clearError();

    try {
      final result = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
        newPasswordConfirmation: newPasswordConfirmation,
      );
      
      if (result['success'] == true) {
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(result['message'] ?? 'Erreur de changement de mot de passe');
        return false;
      }
    } on NetworkException catch (e) {
      _setError(e.message);
      return false;
    } catch (e) {
      _setError('Erreur de changement de mot de passe: ${e.toString()}');
      return false;
    }
  }



  /// Actualise le token
  Future<bool> refreshToken() async {
    if (!isAuthenticated) return false;

    try {
      final result = await _authService.refreshToken();
      
      if (result['success'] == true) {
        _user = result['user'] as UserModel;
        _token = result['token'] as String;
        return true;
      } else {
        // Token invalide, déconnecter l'utilisateur
        await logout();
        return false;
      }
    } catch (e) {
      // Token invalide, déconnecter l'utilisateur
      await logout();
      return false;
    }
  }

  /// Définit l'état
  void _setState(AuthState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// Définit une erreur
  void _setError(String message) {
    _errorMessage = message;
    _setState(AuthState.error);
  }

  /// Efface l'erreur
  void _clearError() {
    _errorMessage = null;
  }

  /// Efface l'état d'erreur
  void clearError() {
    if (_state == AuthState.error) {
      _setState(_user != null ? AuthState.authenticated : AuthState.unauthenticated);
    }
    _clearError();
  }


}
