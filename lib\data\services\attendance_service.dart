import 'package:shared_preferences/shared_preferences.dart';
import '../models/attendance_model.dart';
import '../models/api_response_model.dart';
import '../../core/constants/app_constants.dart';
import '../../core/config/api_endpoints.dart';
import '../../core/utils/exceptions.dart';
import 'http_service.dart';

/// Service pour gérer les présences
class AttendanceService {
  final HttpService _httpService = HttpService();

  /// Récupère le token d'authentification et l'initialise dans HttpService
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(AppConstants.tokenKey);
    if (token != null) {
      _httpService.setAuthToken(token);
    }
    return token;
  }
  /// Check-in de l'employé
  Future<Map<String, dynamic>> checkIn({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await _httpService.post(
        ApiEndpoints.checkin,
        data: {
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return {
        'success': true,
        'attendance': AttendanceModel.fromJson(response['data']),
        'message': response['message'] ?? 'Check-in réussi'
      };
    } on NetworkException catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur lors du check-in: ${e.toString()}'
      };
    }
  }

  /// Check-out de l'employé
  Future<Map<String, dynamic>> checkOut({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await _httpService.post(
        ApiEndpoints.checkout,
        data: {
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      return {
        'success': true,
        'attendance': AttendanceModel.fromJson(response['data']),
        'message': response['message'] ?? 'Check-out réussi'
      };
    } on NetworkException catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur lors du check-out: ${e.toString()}'
      };
    }
  }

  /// Récupère la présence d'aujourd'hui
  Future<Map<String, dynamic>> getTodayAttendance() async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await _httpService.get(ApiEndpoints.todayAttendance);

      return {
        'success': true,
        'attendance': response['data'] != null
            ? AttendanceModel.fromJson(response['data'])
            : null,
        'message': response['message'] ?? 'Présence d\'aujourd\'hui récupérée'
      };
    } on NetworkException catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur lors de la récupération: ${e.toString()}'
      };
    }
  }

  /// Récupère l'historique des présences
  Future<Map<String, dynamic>> getAttendanceHistory({
    DateTime? startDate,
    DateTime? endDate,
    int? page,
    int? perPage,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final Map<String, dynamic> params = {};

      if (startDate != null) {
        params[ApiParams.dateFrom] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        params[ApiParams.dateTo] = endDate.toIso8601String().split('T')[0];
      }

      if (page != null) {
        params[ApiParams.page] = page.toString();
      }

      if (perPage != null) {
        params[ApiParams.perPage] = perPage.toString();
      }

      final response = await _httpService.get(
        ApiEndpoints.attendanceHistory,
        params: params,
      );

      final attendanceList = (response['data'] as List<dynamic>?)
          ?.map((item) => AttendanceModel.fromJson(item as Map<String, dynamic>))
          .toList() ?? [];

      return {
        'success': true,
        'data': attendanceList,
        'pagination': response['meta'],
        'message': response['message'] ?? 'Historique récupéré avec succès'
      };
    } on NetworkException catch (e) {
      return {
        'success': false,
        'message': e.message,
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur lors de la récupération: ${e.toString()}'
      };
    }
  }

  /// Get attendance statistics
  Future<Map<String, dynamic>> getAttendanceStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final Map<String, String> queryParams = {};

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      final uri = Uri.parse('${AppConfig.baseUrl}/attendance/stats')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'stats': data['stats'],
          'message': 'Attendance statistics retrieved successfully'
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to get attendance statistics: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Connection error: $e'
      };
    }
  }
}

