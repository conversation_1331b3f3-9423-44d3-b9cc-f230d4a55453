import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../../core/config/app_config.dart';

class AttendanceService {

  /// Récupère le token d'authentification
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(AppConstants.tokenKey);
  }
  Future<Map<String, dynamic>> checkIn({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/attendance/checkin'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'attendance': data['attendance'],
          'message': 'Check-in réussi'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors du check-in: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  Future<Map<String, dynamic>> checkOut({
    required double latitude,
    required double longitude,
    required String location,
    String? notes,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.post(
        Uri.parse('${AppConfig.baseUrl}/attendance/checkout'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'latitude': latitude,
          'longitude': longitude,
          'location': location,
          'notes': notes,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'attendance': data['attendance'],
          'message': 'Check-out réussi'
        };
      } else {
        return {
          'success': false,
          'message': 'Erreur lors du check-out: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Erreur de connexion: $e'
      };
    }
  }

  /// Get today's attendance record
  Future<Map<String, dynamic>> getTodayAttendance() async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final response = await http.get(
        Uri.parse('${AppConfig.baseUrl}/attendance/today'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'attendance': data['attendance'],
          'message': 'Today\'s attendance retrieved successfully'
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to get today\'s attendance: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Connection error: $e'
      };
    }
  }

  /// Get attendance history
  Future<Map<String, dynamic>> getAttendanceHistory({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final Map<String, String> queryParams = {};

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      final uri = Uri.parse('${AppConfig.baseUrl}/attendance/history')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'data': data['data'],
          'message': 'Attendance history retrieved successfully'
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to get attendance history: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Connection error: $e'
      };
    }
  }

  /// Get attendance statistics
  Future<Map<String, dynamic>> getAttendanceStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return {
          'success': false,
          'message': 'Token d\'authentification manquant'
        };
      }

      final Map<String, String> queryParams = {};

      if (startDate != null) {
        queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      }

      if (endDate != null) {
        queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      }

      final uri = Uri.parse('${AppConfig.baseUrl}/attendance/stats')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      ).timeout(AppConstants.requestTimeoutDuration);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'stats': data['stats'],
          'message': 'Attendance statistics retrieved successfully'
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to get attendance statistics: ${response.statusCode}'
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Connection error: $e'
      };
    }
  }
}

