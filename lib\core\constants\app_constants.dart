/// Application constants
class AppConstants {
  static const String appName = 'ClockIn Mobile';
  static const String baseUrl = 'http://localhost:8000/api';

  // API Endpoints
  static const String loginEndpoint = '/auth/login';
  static const String logoutEndpoint = '/auth/logout';
  static const String profileEndpoint = '/user/profile';
  static const String attendanceEndpoint = '/attendance';
  static const String reportsEndpoint = '/reports';

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String locationKey = 'last_location';
  static const String settingsKey = 'app_settings';

  // UI Constants
  static const double spacingXS = 4.0;
  static const double spacingS = 8.0;
  static const double spacingM = 16.0;
  static const double spacingL = 24.0;
  static const double spacingXL = 32.0;
  static const double spacingXXL = 48.0;

  static const double borderRadius = 12.0;
  static const double borderRadiusS = 8.0;
  static const double borderRadiusL = 16.0;

  static const double buttonHeight = 48.0;
  static const double buttonHeightS = 36.0;
  static const double buttonHeightL = 56.0;

  static const double iconSize = 24.0;
  static const double iconSizeS = 16.0;
  static const double iconSizeL = 32.0;
  static const double iconSizeXL = 48.0;

  // App Settings
  static const int requestTimeout = 30000; // 30 seconds
  static const Duration requestTimeoutDuration = Duration(milliseconds: requestTimeout);
  static const String appVersion = '1.0.0';
  static const int maxRetries = 3;
  static const int cacheTimeout = 300000; // 5 minutes

  // Location Settings
  static const double locationAccuracy = 10.0; // meters
  static const int locationTimeout = 15000; // 15 seconds

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Messages
  static const String checkinSuccess = 'Check-in successful!';
  static const String checkoutSuccess = 'Check-out successful!';
  static const String checkinFailed = 'Check-in failed. Please try again.';
  static const String checkoutFailed = 'Check-out failed. Please try again.';

  // Error Messages
  static const String networkError = 'Network error. Please check your connection.';
  static const String locationError = 'Unable to get your location. Please enable GPS.';
  static const String permissionError = 'Permission denied. Please grant required permissions.';
  static const String unknownError = 'An unexpected error occurred. Please try again.';
}


