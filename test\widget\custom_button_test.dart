import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/presentation/widgets/common/custom_button.dart';

void main() {
  group('CustomButton Widget Tests', () {
    testWidgets('should render custom button correctly', (WidgetTester tester) async {
      bool wasPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Test Button',
              onPressed: () {
                wasPressed = true;
              },
            ),
          ),
        ),
      );

      // Find the button
      final buttonFinder = find.byType(CustomButton);
      expect(buttonFinder, findsOneWidget);

      // Find the text
      final textFinder = find.text('Test Button');
      expect(textFinder, findsOneWidget);

      // Tap the button
      await tester.tap(buttonFinder);
      await tester.pump();

      // Verify callback was called
      expect(wasPressed, isTrue);
    });

    testWidgets('should render outlined button correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Outlined Button',
              onPressed: () {},
              isOutlined: true,
            ),
          ),
        ),
      );

      final buttonFinder = find.byType(CustomButton);
      expect(buttonFinder, findsOneWidget);

      final textFinder = find.text('Outlined Button');
      expect(textFinder, findsOneWidget);
    });

    testWidgets('should show loading state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Loading Button',
              onPressed: () {},
              isLoading: true,
            ),
          ),
        ),
      );

      // Should not find the text when loading
      final textFinder = find.text('Loading Button');
      expect(textFinder, findsNothing);

      // Should find loading indicator
      final loadingFinder = find.byType(CircularProgressIndicator);
      expect(loadingFinder, findsOneWidget);
    });

    testWidgets('should be disabled when onPressed is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Disabled Button',
              onPressed: null,
            ),
          ),
        ),
      );

      final buttonFinder = find.byType(CustomButton);
      expect(buttonFinder, findsOneWidget);

      // Try to tap the button
      await tester.tap(buttonFinder);
      await tester.pump();

      // Button should still be there but disabled
      expect(buttonFinder, findsOneWidget);
    });

    testWidgets('should render button with icon correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Button with Icon',
              icon: Icons.add,
              onPressed: () {},
            ),
          ),
        ),
      );

      final buttonFinder = find.byType(CustomButton);
      expect(buttonFinder, findsOneWidget);

      final textFinder = find.text('Button with Icon');
      expect(textFinder, findsOneWidget);

      final iconFinder = find.byIcon(Icons.add);
      expect(iconFinder, findsOneWidget);
    });

    testWidgets('should handle different button heights', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                CustomButton(
                  text: 'Small Button',
                  onPressed: () {},
                  height: 40,
                ),
                CustomButton(
                  text: 'Medium Button',
                  onPressed: () {},
                  height: 50,
                ),
                CustomButton(
                  text: 'Large Button',
                  onPressed: () {},
                  height: 60,
                ),
              ],
            ),
          ),
        ),
      );

      final smallButtonFinder = find.text('Small Button');
      expect(smallButtonFinder, findsOneWidget);

      final mediumButtonFinder = find.text('Medium Button');
      expect(mediumButtonFinder, findsOneWidget);

      final largeButtonFinder = find.text('Large Button');
      expect(largeButtonFinder, findsOneWidget);
    });

    testWidgets('should not be tappable when loading', (WidgetTester tester) async {
      bool wasPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Loading Button',
              onPressed: () {
                wasPressed = true;
              },
              isLoading: true,
            ),
          ),
        ),
      );

      final buttonFinder = find.byType(CustomButton);

      // Try to tap the button while loading
      await tester.tap(buttonFinder);
      await tester.pump();

      // Callback should not be called when loading
      expect(wasPressed, isFalse);
    });

    testWidgets('should apply custom colors correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomButton(
              text: 'Custom Color Button',
              onPressed: () {},
              backgroundColor: Colors.purple,
              textColor: Colors.yellow,
            ),
          ),
        ),
      );

      final buttonFinder = find.byType(CustomButton);
      expect(buttonFinder, findsOneWidget);

      // Get the button widget
      final button = tester.widget<CustomButton>(buttonFinder);
      expect(button.backgroundColor, equals(Colors.purple));
      expect(button.textColor, equals(Colors.yellow));
    });

    group('Button Accessibility', () {
      testWidgets('should have proper semantics', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CustomButton(
                text: 'Accessible Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        // Check for semantic properties
        expect(find.bySemanticsLabel('Accessible Button'), findsOneWidget);
      });

      testWidgets('should be excluded from semantics when disabled', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CustomButton(
                text: 'Disabled Button',
                onPressed: null,
              ),
            ),
          ),
        );

        final buttonFinder = find.byType(CustomButton);
        expect(buttonFinder, findsOneWidget);
      });
    });

    group('Button Styling', () {
      testWidgets('should have correct default styling for custom button', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CustomButton(
                text: 'Custom Button',
                onPressed: () {},
              ),
            ),
          ),
        );

        final buttonFinder = find.byType(CustomButton);
        final button = tester.widget<CustomButton>(buttonFinder);

        // Check default values
        expect(button.backgroundColor, isNull); // Should use default
        expect(button.textColor, isNull); // Should use default
        expect(button.isLoading, isFalse);
        expect(button.isOutlined, isFalse);
      });
    });
  });
}
