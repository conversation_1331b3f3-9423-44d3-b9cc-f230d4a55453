import 'package:flutter_test/flutter_test.dart';
import 'package:clockin_mobile/data/models/attendance_model.dart';

void main() {
  group('AttendanceModel Tests', () {
    final testJson = {
      'id': 1,
      'user_id': 123,
      'date': '2024-01-15',
      'checkin_time': '2024-01-15T09:00:00.000Z',
      'checkout_time': '2024-01-15T17:30:00.000Z',
      'checkin_location': 'Office Building A',
      'checkout_location': 'Office Building A',
      'checkin_latitude': 40.7128,
      'checkin_longitude': -74.0060,
      'checkout_latitude': 40.7128,
      'checkout_longitude': -74.0060,
      'status': 'completed',
      'notes': 'Regular work day',
      'total_hours': 8.5,
      'created_at': '2024-01-15T09:00:00.000Z',
      'updated_at': '2024-01-15T17:30:00.000Z',
    };

    final testAttendance = AttendanceModel(
      id: 1,
      userId: 123,
      date: DateTime.parse('2024-01-15'),
      checkinTime: DateTime.parse('2024-01-15T09:00:00.000Z'),
      checkoutTime: DateTime.parse('2024-01-15T17:30:00.000Z'),
      checkinLocation: 'Office Building A',
      checkoutLocation: 'Office Building A',
      checkinLatitude: 40.7128,
      checkinLongitude: -74.0060,
      checkoutLatitude: 40.7128,
      checkoutLongitude: -74.0060,
      status: 'completed',
      notes: 'Regular work day',
      totalHours: const Duration(hours: 8, minutes: 30),
      createdAt: DateTime.parse('2024-01-15T09:00:00.000Z'),
      updatedAt: DateTime.parse('2024-01-15T17:30:00.000Z'),
    );

    group('JSON Serialization', () {
      test('should create AttendanceModel from JSON correctly', () {
        final attendance = AttendanceModel.fromJson(testJson);

        expect(attendance.id, equals(1));
        expect(attendance.userId, equals(123));
        expect(attendance.date, equals(DateTime.parse('2024-01-15')));
        expect(attendance.checkinTime, equals(DateTime.parse('2024-01-15T09:00:00.000Z')));
        expect(attendance.checkoutTime, equals(DateTime.parse('2024-01-15T17:30:00.000Z')));
        expect(attendance.checkinLocation, equals('Office Building A'));
        expect(attendance.checkoutLocation, equals('Office Building A'));
        expect(attendance.checkinLatitude, equals(40.7128));
        expect(attendance.checkinLongitude, equals(-74.0060));
        expect(attendance.checkoutLatitude, equals(40.7128));
        expect(attendance.checkoutLongitude, equals(-74.0060));
        expect(attendance.status, equals('completed'));
        expect(attendance.notes, equals('Regular work day'));
        expect(attendance.totalHours, equals(const Duration(hours: 8, minutes: 30)));
        expect(attendance.createdAt, equals(DateTime.parse('2024-01-15T09:00:00.000Z')));
        expect(attendance.updatedAt, equals(DateTime.parse('2024-01-15T17:30:00.000Z')));
      });

      test('should convert AttendanceModel to JSON correctly', () {
        final json = testAttendance.toJson();

        expect(json['id'], equals(1));
        expect(json['user_id'], equals(123));
        expect(json['date'], equals('2024-01-15'));
        expect(json['checkin_time'], equals('2024-01-15T09:00:00.000Z'));
        expect(json['checkout_time'], equals('2024-01-15T17:30:00.000Z'));
        expect(json['checkin_location'], equals('Office Building A'));
        expect(json['checkout_location'], equals('Office Building A'));
        expect(json['checkin_latitude'], equals(40.7128));
        expect(json['checkin_longitude'], equals(-74.0060));
        expect(json['checkout_latitude'], equals(40.7128));
        expect(json['checkout_longitude'], equals(-74.0060));
        expect(json['status'], equals('completed'));
        expect(json['notes'], equals('Regular work day'));
        expect(json['total_hours'], equals(8.5));
        expect(json['created_at'], equals('2024-01-15T09:00:00.000Z'));
        expect(json['updated_at'], equals('2024-01-15T17:30:00.000Z'));
      });

      test('should handle null optional fields in JSON', () {
        final jsonWithNulls = {
          'id': 1,
          'user_id': 123,
          'date': '2024-01-15',
          'status': 'in_progress',
          'created_at': '2024-01-15T09:00:00.000Z',
          'updated_at': '2024-01-15T09:00:00.000Z',
          // Optional fields are null
          'checkin_time': null,
          'checkout_time': null,
          'checkin_location': null,
          'checkout_location': null,
          'checkin_latitude': null,
          'checkin_longitude': null,
          'checkout_latitude': null,
          'checkout_longitude': null,
          'notes': null,
          'total_hours': null,
        };

        final attendance = AttendanceModel.fromJson(jsonWithNulls);

        expect(attendance.id, equals(1));
        expect(attendance.userId, equals(123));
        expect(attendance.date, equals(DateTime.parse('2024-01-15')));
        expect(attendance.status, equals('in_progress'));
        expect(attendance.checkinTime, isNull);
        expect(attendance.checkoutTime, isNull);
        expect(attendance.checkinLocation, isNull);
        expect(attendance.checkoutLocation, isNull);
        expect(attendance.checkinLatitude, isNull);
        expect(attendance.checkinLongitude, isNull);
        expect(attendance.checkoutLatitude, isNull);
        expect(attendance.checkoutLongitude, isNull);
        expect(attendance.notes, isNull);
        expect(attendance.totalHours, isNull);
      });

      test('should handle partial attendance (only check-in)', () {
        final partialJson = {
          'id': 1,
          'user_id': 123,
          'date': '2024-01-15',
          'checkin_time': '2024-01-15T09:00:00.000Z',
          'checkin_location': 'Office Building A',
          'checkin_latitude': 40.7128,
          'checkin_longitude': -74.0060,
          'status': 'in_progress',
          'created_at': '2024-01-15T09:00:00.000Z',
          'updated_at': '2024-01-15T09:00:00.000Z',
        };

        final attendance = AttendanceModel.fromJson(partialJson);

        expect(attendance.checkinTime, isNotNull);
        expect(attendance.checkoutTime, isNull);
        expect(attendance.checkinLocation, equals('Office Building A'));
        expect(attendance.checkoutLocation, isNull);
        expect(attendance.status, equals('in_progress'));
      });
    });

    group('Equality and Props', () {
      test('should be equal when all properties are the same', () {
        final attendance1 = AttendanceModel.fromJson(testJson);
        final attendance2 = AttendanceModel.fromJson(testJson);

        expect(attendance1, equals(attendance2));
        expect(attendance1.hashCode, equals(attendance2.hashCode));
      });

      test('should not be equal when properties differ', () {
        final attendance1 = AttendanceModel.fromJson(testJson);
        final modifiedJson = Map<String, dynamic>.from(testJson);
        modifiedJson['status'] = 'in_progress';
        final attendance2 = AttendanceModel.fromJson(modifiedJson);

        expect(attendance1, isNot(equals(attendance2)));
        expect(attendance1.hashCode, isNot(equals(attendance2.hashCode)));
      });

      test('should have correct props for Equatable', () {
        final attendance = AttendanceModel.fromJson(testJson);
        final props = attendance.props;

        expect(props, contains(attendance.id));
        expect(props, contains(attendance.userId));
        expect(props, contains(attendance.date));
        expect(props, contains(attendance.checkinTime));
        expect(props, contains(attendance.checkoutTime));
        expect(props, contains(attendance.status));
      });
    });

    group('Duration Calculations', () {
      test('should calculate total hours correctly from times', () {
        final checkinTime = DateTime.parse('2024-01-15T09:00:00.000Z');
        final checkoutTime = DateTime.parse('2024-01-15T17:30:00.000Z');
        
        // Calculate expected duration directly
        final expectedDuration = checkoutTime.difference(checkinTime);
        expect(expectedDuration.inHours, equals(8));
        expect(expectedDuration.inMinutes, equals(510)); // 8.5 hours
      });

      test('should handle null total hours', () {
        final jsonWithoutTotalHours = Map<String, dynamic>.from(testJson);
        jsonWithoutTotalHours.remove('total_hours');

        final attendance = AttendanceModel.fromJson(jsonWithoutTotalHours);
        expect(attendance.totalHours, isNull);
      });

      test('should convert hours to duration correctly', () {
        final jsonWithHours = Map<String, dynamic>.from(testJson);
        jsonWithHours['total_hours'] = 7.25; // 7 hours 15 minutes

        final attendance = AttendanceModel.fromJson(jsonWithHours);
        expect(attendance.totalHours, isNotNull);
        expect(attendance.totalHours!.inHours, equals(7));
        expect(attendance.totalHours!.inMinutes, equals(435)); // 7.25 hours
      });
    });

    group('Status Validation', () {
      test('should handle different status values', () {
        final statuses = ['in_progress', 'completed', 'absent', 'late'];
        
        for (final status in statuses) {
          final jsonWithStatus = Map<String, dynamic>.from(testJson);
          jsonWithStatus['status'] = status;
          
          final attendance = AttendanceModel.fromJson(jsonWithStatus);
          expect(attendance.status, equals(status));
        }
      });
    });

    group('Location Data', () {
      test('should handle location coordinates correctly', () {
        final attendance = AttendanceModel.fromJson(testJson);
        
        expect(attendance.checkinLatitude, isA<double>());
        expect(attendance.checkinLongitude, isA<double>());
        expect(attendance.checkoutLatitude, isA<double>());
        expect(attendance.checkoutLongitude, isA<double>());
        
        // Validate coordinate ranges
        expect(attendance.checkinLatitude!, greaterThanOrEqualTo(-90));
        expect(attendance.checkinLatitude!, lessThanOrEqualTo(90));
        expect(attendance.checkinLongitude!, greaterThanOrEqualTo(-180));
        expect(attendance.checkinLongitude!, lessThanOrEqualTo(180));
      });

      test('should handle missing location data', () {
        final jsonWithoutLocation = Map<String, dynamic>.from(testJson);
        jsonWithoutLocation.remove('checkin_latitude');
        jsonWithoutLocation.remove('checkin_longitude');
        jsonWithoutLocation.remove('checkout_latitude');
        jsonWithoutLocation.remove('checkout_longitude');

        final attendance = AttendanceModel.fromJson(jsonWithoutLocation);
        
        expect(attendance.checkinLatitude, isNull);
        expect(attendance.checkinLongitude, isNull);
        expect(attendance.checkoutLatitude, isNull);
        expect(attendance.checkoutLongitude, isNull);
      });
    });

    group('String Representation', () {
      test('should have meaningful toString', () {
        final attendance = AttendanceModel.fromJson(testJson);
        final stringRep = attendance.toString();

        expect(stringRep, contains('AttendanceModel'));
        expect(stringRep, contains('completed'));
      });
    });
  });
}
