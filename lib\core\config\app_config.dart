import '../constants/app_constants.dart';

/// Environment types
enum Environment { development, staging, production }

/// Application configuration
class AppConfig {
  // Private constructor to prevent instantiation
  AppConfig._();

  /// Current environment
  static Environment _environment = Environment.development;

  /// Get current environment
  static Environment get environment => _environment;

  /// Set environment
  static void setEnvironment(Environment env) {
    _environment = env;
  }

  /// Check if in development mode
  static bool get isDevelopment => _environment == Environment.development;

  /// Check if in staging mode
  static bool get isStaging => _environment == Environment.staging;

  /// Check if in production mode
  static bool get isProduction => _environment == Environment.production;

  /// Get base URL based on environment
  static String get baseUrl {
    switch (_environment) {
      case Environment.development:
        return 'http://127.0.0.1:8000/api';
      case Environment.staging:
        return 'https://staging.clockin.com/api';
      case Environment.production:
        return 'https://clockin.com/api';
    }
  }

  /// Get app name with environment suffix
  static String get appName {
    switch (_environment) {
      case Environment.development:
        return '${AppConstants.appName} (Dev)';
      case Environment.staging:
        return '${AppConstants.appName} (Staging)';
      case Environment.production:
        return AppConstants.appName;
    }
  }

  /// Debug settings
  static bool get enableLogging => isDevelopment || isStaging;
  static bool get enableDebugMode => isDevelopment;
  static bool get enableCrashReporting => isStaging || isProduction;

  /// API settings
  static int get requestTimeout {
    switch (_environment) {
      case Environment.development:
        return 60000; // 60 seconds for development
      case Environment.staging:
        return 45000; // 45 seconds for staging
      case Environment.production:
        return AppConstants.requestTimeout; // 30 seconds for production
    }
  }

  /// Cache settings
  static int get cacheTimeout {
    switch (_environment) {
      case Environment.development:
        return 60000; // 1 minute for development
      case Environment.staging:
        return 180000; // 3 minutes for staging
      case Environment.production:
        return AppConstants.cacheTimeout; // 5 minutes for production
    }
  }

  /// Location settings
  static double get locationAccuracy {
    switch (_environment) {
      case Environment.development:
        return 50.0; // Less accurate for development
      case Environment.staging:
        return 20.0; // Medium accuracy for staging
      case Environment.production:
        return AppConstants.locationAccuracy; // High accuracy for production
    }
  }

  /// Feature flags
  static bool get enableBiometricAuth => isStaging || isProduction;
  static bool get enableOfflineMode => true;
  static bool get enablePushNotifications => isStaging || isProduction;
  static bool get enableAnalytics => isStaging || isProduction;

  /// Database settings
  static String get databaseName {
    switch (_environment) {
      case Environment.development:
        return 'clockin_dev.db';
      case Environment.staging:
        return 'clockin_staging.db';
      case Environment.production:
        return 'clockin.db';
    }
  }

  /// Encryption settings
  static String get encryptionKey {
    // In a real app, this should be stored securely
    switch (_environment) {
      case Environment.development:
        return 'dev_encryption_key_12345';
      case Environment.staging:
        return 'staging_encryption_key_67890';
      case Environment.production:
        return 'prod_encryption_key_abcdef';
    }
  }

  /// Initialize configuration
  static void initialize({Environment? environment}) {
    if (environment != null) {
      setEnvironment(environment);
    }
    
    // Log current configuration
    if (enableLogging) {
      print('AppConfig initialized:');
      print('  Environment: $_environment');
      print('  Base URL: $baseUrl');
      print('  App Name: $appName');
      print('  Debug Mode: $enableDebugMode');
      print('  Logging: $enableLogging');
    }
  }

  /// Get configuration as map
  static Map<String, dynamic> toMap() {
    return {
      'environment': _environment.toString(),
      'baseUrl': baseUrl,
      'appName': appName,
      'enableLogging': enableLogging,
      'enableDebugMode': enableDebugMode,
      'enableCrashReporting': enableCrashReporting,
      'requestTimeout': requestTimeout,
      'cacheTimeout': cacheTimeout,
      'locationAccuracy': locationAccuracy,
      'enableBiometricAuth': enableBiometricAuth,
      'enableOfflineMode': enableOfflineMode,
      'enablePushNotifications': enablePushNotifications,
      'enableAnalytics': enableAnalytics,
      'databaseName': databaseName,
    };
  }

  /// Validate configuration
  static bool validate() {
    try {
      // Check if base URL is valid
      Uri.parse(baseUrl);
      
      // Check if timeouts are positive
      if (requestTimeout <= 0 || cacheTimeout <= 0) {
        return false;
      }
      
      // Check if location accuracy is positive
      if (locationAccuracy <= 0) {
        return false;
      }
      
      return true;
    } catch (e) {
      if (enableLogging) {
        print('Configuration validation failed: $e');
      }
      return false;
    }
  }
}

/// Configuration builder for different environments
class AppConfigBuilder {
  Environment _environment = Environment.development;
  String? _customBaseUrl;
  int? _customTimeout;
  double? _customLocationAccuracy;

  /// Set environment
  AppConfigBuilder environment(Environment env) {
    _environment = env;
    return this;
  }

  /// Set custom base URL
  AppConfigBuilder baseUrl(String url) {
    _customBaseUrl = url;
    return this;
  }

  /// Set custom timeout
  AppConfigBuilder timeout(int timeout) {
    _customTimeout = timeout;
    return this;
  }

  /// Set custom location accuracy
  AppConfigBuilder locationAccuracy(double accuracy) {
    _customLocationAccuracy = accuracy;
    return this;
  }

  /// Build and apply configuration
  void build() {
    AppConfig.setEnvironment(_environment);
    
    // Apply custom settings if provided
    // Note: In a real implementation, you might want to store these
    // custom settings in a separate configuration object
    
    AppConfig.initialize(environment: _environment);
  }
}
