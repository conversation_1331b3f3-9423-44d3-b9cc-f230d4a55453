/// Custom exceptions for the application
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  @override
  String toString() => 'AppException: $message';
}

/// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.details});
}

/// Authentication related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.details});
}

/// Location related exceptions
class LocationException extends AppException {
  const LocationException(super.message, {super.code, super.details});
}

/// Validation related exceptions
class ValidationException extends AppException {
  const ValidationException(super.message, {super.code, super.details});
}

/// Storage related exceptions
class StorageException extends AppException {
  const StorageException(super.message, {super.code, super.details});
}

/// Permission related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code, super.details});
}

/// Server related exceptions
class ServerException extends AppException {
  const ServerException(super.message, {super.code, super.details});
}

/// Cache related exceptions
class CacheException extends AppException {
  const CacheException(super.message, {super.code, super.details});
}

/// Timeout related exceptions
class TimeoutException extends AppException {
  const TimeoutException(super.message, {super.code, super.details});
}

/// Unknown or unexpected exceptions
class UnknownException extends AppException {
  const UnknownException(super.message, {super.code, super.details});
}

/// Exception handler utility
class ExceptionHandler {
  // Private constructor to prevent instantiation
  ExceptionHandler._();

  /// Handle and convert exceptions to user-friendly messages
  static String getErrorMessage(dynamic error) {
    if (error is AppException) {
      return error.message;
    }
    
    if (error is FormatException) {
      return 'Invalid data format received';
    }
    
    if (error is TypeError) {
      return 'Data type error occurred';
    }
    
    if (error is ArgumentError) {
      return 'Invalid argument provided';
    }
    
    // Default message for unknown errors
    return 'An unexpected error occurred. Please try again.';
  }

  /// Get error code from exception
  static String? getErrorCode(dynamic error) {
    if (error is AppException) {
      return error.code;
    }
    return null;
  }

  /// Check if error is network related
  static bool isNetworkError(dynamic error) {
    return error is NetworkException ||
           error.toString().toLowerCase().contains('network') ||
           error.toString().toLowerCase().contains('connection') ||
           error.toString().toLowerCase().contains('timeout');
  }

  /// Check if error is authentication related
  static bool isAuthError(dynamic error) {
    return error is AuthException ||
           error.toString().toLowerCase().contains('unauthorized') ||
           error.toString().toLowerCase().contains('authentication') ||
           error.toString().toLowerCase().contains('token');
  }

  /// Check if error is permission related
  static bool isPermissionError(dynamic error) {
    return error is PermissionException ||
           error.toString().toLowerCase().contains('permission') ||
           error.toString().toLowerCase().contains('denied');
  }

  /// Check if error is location related
  static bool isLocationError(dynamic error) {
    return error is LocationException ||
           error.toString().toLowerCase().contains('location') ||
           error.toString().toLowerCase().contains('gps');
  }

  /// Log error for debugging
  static void logError(dynamic error, [StackTrace? stackTrace]) {
    // In production, you might want to send this to a crash reporting service
    print('Error: $error');
    if (stackTrace != null) {
      print('Stack trace: $stackTrace');
    }
  }

  /// Create appropriate exception from HTTP status code
  static AppException fromHttpStatusCode(int statusCode, String message) {
    switch (statusCode) {
      case 400:
        return ValidationException(message, code: 'BAD_REQUEST');
      case 401:
        return AuthException(message, code: 'UNAUTHORIZED');
      case 403:
        return AuthException(message, code: 'FORBIDDEN');
      case 404:
        return ServerException(message, code: 'NOT_FOUND');
      case 408:
        return TimeoutException(message, code: 'REQUEST_TIMEOUT');
      case 422:
        return ValidationException(message, code: 'UNPROCESSABLE_ENTITY');
      case 429:
        return ServerException(message, code: 'TOO_MANY_REQUESTS');
      case 500:
        return ServerException(message, code: 'INTERNAL_SERVER_ERROR');
      case 502:
        return NetworkException(message, code: 'BAD_GATEWAY');
      case 503:
        return ServerException(message, code: 'SERVICE_UNAVAILABLE');
      case 504:
        return TimeoutException(message, code: 'GATEWAY_TIMEOUT');
      default:
        if (statusCode >= 400 && statusCode < 500) {
          return ValidationException(message, code: 'CLIENT_ERROR');
        } else if (statusCode >= 500) {
          return ServerException(message, code: 'SERVER_ERROR');
        } else {
          return UnknownException(message, code: 'UNKNOWN_HTTP_ERROR');
        }
    }
  }
}

/// Result wrapper for operations that can fail
class Result<T> {
  final T? data;
  final AppException? error;
  final bool isSuccess;

  const Result.success(this.data) : error = null, isSuccess = true;
  const Result.failure(this.error) : data = null, isSuccess = false;

  /// Check if the result is successful
  bool get isFailure => !isSuccess;

  /// Get data or throw exception
  T get dataOrThrow {
    if (isSuccess && data != null) {
      return data!;
    }
    throw error ?? UnknownException('Unknown error occurred');
  }

  /// Get data or return default value
  T getDataOrDefault(T defaultValue) {
    return isSuccess && data != null ? data! : defaultValue;
  }

  /// Transform the data if successful
  Result<R> map<R>(R Function(T) transform) {
    if (isSuccess && data != null) {
      try {
        return Result.success(transform(data!));
      } catch (e) {
        return Result.failure(UnknownException('Transform failed: $e'));
      }
    }
    return Result.failure(error!);
  }

  /// Handle both success and failure cases
  R fold<R>(
    R Function(T) onSuccess,
    R Function(AppException) onFailure,
  ) {
    if (isSuccess && data != null) {
      return onSuccess(data!);
    }
    return onFailure(error!);
  }
}
